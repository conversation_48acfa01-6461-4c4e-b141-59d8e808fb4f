const dashboardHTML=`
<div class="dashboard-component">
  <div class="dashboard-header">
    <div class="dashboard-header-content">
      <h1>Dashboard</h1>
      <div class="database-container">
        <div class="database-row">
        <div class="database-left">
          <!-- Time Tracker Components -->
          <div class="time-tracker-container">
            <!-- Local Time Block -->
            <div class="time-tracker-block">
              <span class="time-tracker-label">Local Time</span>
              <div class="time-tracker-content">
                <img src="./assets/timezone-ic.svg" alt="Timezone Icon" class="time-tracker-icon" width="12" height="12" />
                <div class="time-tracker-time" id="local-time-display">12:00:00 AM</div>
              </div>
            </div>

            <!-- Divider between time trackers -->
            <div class="time-tracker-divider"></div>

            <!-- Pacific Time Block -->
            <div class="time-tracker-block">
              <span class="time-tracker-label">Pacific Time</span>
              <div class="time-tracker-content">
                <img src="./assets/timezone-ic.svg" alt="Timezone Icon" class="time-tracker-icon" width="12" height="12" />
                <div class="time-tracker-time" id="pacific-time-display">12:00:00 AM</div>
              </div>
            </div>
          </div>

          <!-- Divider between time tracker and database -->
          <div class="database-time-divider"></div>

          <div class="database-stat database-status">
            <div class="database-status-row" style="display: flex; align-items: center; gap: 12px;">
              <div style="display: flex; flex-direction: column;">
                <span class="database-label-row">
                  <img class="database-icon" src="./assets/data-cell-ic.svg" alt="Database Icon" width="12" height="12" />
                  <span class="database-label">Database</span>
                </span>
                <span class="database-updated-row">
                  <img src="./assets/update-time-ic.svg" alt="Updated Icon" width="12" height="12" />
                  <span class="database-updated-text">Updated 5 min ago</span>
                </span>
              </div>
              <button class="database-update-btn action-button">Update</button>
            </div>
          </div>
        </div>
        <div class="database-right" style="display: flex; align-items: center; gap: 16px;">
          <div id="dashboard-privacy-mode-mount"></div>
          <div id="dashboard-marketplace-dropdown-mount"></div>
        </div>
      </div>
    </div>
    </div>
  </div>
  <div class="dashboard-content">
    <div class="account-status">
      <div class="account-status-header">
        <div class="account-status-title">
          <img src="./assets/account-status-ic.svg" alt="Account Status" class="account-status-icon">
          <span>Account Status</span>
        </div>
      </div>
      <div class="account-status-metrics">
        <div class="account-status-tier">
          <div class="tier-info">
            <span class="tier-label">Current Tier</span>
            <span class="tier-value gradient-text">20,000</span>
          </div>
        </div>
        <div class="metric-item">
          <div class="metric-header">
            <span class="metric-label">Designs</span>
            <span class="metric-percentage zero" style="display:none;">0%</span>
          </div>
          <div class="metric-subtext">17,748 of 20000<span class="metric-remaining"></span></div>
          <div class="progress-bar">
            <div class="progress-track"></div>
            <div class="progress-fill" style="width: 0%"></div>
          </div>
        </div>
        <div class="metric-item">
          <div class="metric-header">
            <span class="metric-label">Products</span>
            <span class="metric-percentage zero" style="display:none;">0%</span>
          </div>
          <div class="metric-subtext">1,129,440 of 1,620,000<span class="metric-remaining"></span></div>
          <div class="progress-bar">
            <div class="progress-track"></div>
            <div class="progress-fill" style="width: 0%"></div>
          </div>
        </div>
        <div class="metric-item">
          <div class="metric-header">
            <span class="metric-label">Today's Quota</span>
            <span class="metric-percentage zero" style="display:none;">0%</span>
          </div>
          <div class="metric-subtext">444 of 2000<span class="metric-remaining"></span></div>
          <div class="progress-bar">
            <div class="progress-track"></div>
            <div class="progress-fill" style="width: 0%"></div>
          </div>
        </div>
      </div>
    </div>
    <!-- Listings Status Overview (Figma-accurate, only dividers between cards) -->
    <div class="listings-status-overview">
      <div class="listings-status-row">
        <div class="listing-status-card" data-tooltip="Live Listings">
          <div class="listing-status-top">
            <span class="listing-status-icon">
              <img src="./assets/data-cell-ic.svg" alt="Live Icon" width="12" height="12" />
            </span>
            <span class="listing-status-number live">1,233,432</span>
          </div>
          <span class="listing-status-label">LIVE</span>
        </div>
        <span class="listing-status-divider"><svg width="1" height="35" viewBox="0 0 1 35" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="35" fill="#606F95" fill-opacity="0.1"/></svg></span>
        <div class="listing-status-card" data-tooltip="Drafts">
          <div class="listing-status-top">
            <span class="listing-status-icon">
              <img src="./assets/data-cell-ic.svg" alt="Draft Icon" width="12" height="12" />
            </span>
            <span class="listing-status-number">12</span>
          </div>
          <span class="listing-status-label">DFT</span>
        </div>
        <span class="listing-status-divider"><svg width="1" height="35" viewBox="0 0 1 35" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="35" fill="#606F95" fill-opacity="0.1"/></svg></span>
        <div class="listing-status-card" data-tooltip="Auto Uploaded">
          <div class="listing-status-top">
            <span class="listing-status-icon">
              <img src="./assets/data-cell-ic.svg" alt="Auto Icon" width="12" height="12" />
            </span>
            <span class="listing-status-number">12,432</span>
          </div>
          <span class="listing-status-label">AUTO</span>
        </div>
        <span class="listing-status-divider"><svg width="1" height="35" viewBox="0 0 1 35" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="35" fill="#606F95" fill-opacity="0.1"/></svg></span>
        <div class="listing-status-card" data-tooltip="In Review">
          <div class="listing-status-top">
            <span class="listing-status-icon">
              <img src="./assets/data-cell-ic.svg" alt="Review Icon" width="12" height="12" />
            </span>
            <span class="listing-status-number">0</span>
          </div>
          <span class="listing-status-label">REV</span>
        </div>
        <span class="listing-status-divider"><svg width="1" height="35" viewBox="0 0 1 35" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="35" fill="#606F95" fill-opacity="0.1"/></svg></span>
        <div class="listing-status-card" data-tooltip="Processing">
          <div class="listing-status-top">
            <span class="listing-status-icon">
              <img src="./assets/data-cell-ic.svg" alt="Processing Icon" width="12" height="12" />
            </span>
            <span class="listing-status-number">0</span>
          </div>
          <span class="listing-status-label">PROC</span>
        </div>
        <span class="listing-status-divider"><svg width="1" height="35" viewBox="0 0 1 35" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="35" fill="#606F95" fill-opacity="0.1"/></svg></span>
        <div class="listing-status-card" data-tooltip="Translating">
          <div class="listing-status-top">
            <span class="listing-status-icon">
              <img src="./assets/data-cell-ic.svg" alt="Translating Icon" width="12" height="12" />
            </span>
            <span class="listing-status-number">3</span>
          </div>
          <span class="listing-status-label">TRNS</span>
        </div>
        <span class="listing-status-divider"><svg width="1" height="35" viewBox="0 0 1 35" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="35" fill="#606F95" fill-opacity="0.1"/></svg></span>
        <div class="listing-status-card" data-tooltip="Locked">
          <div class="listing-status-top">
            <span class="listing-status-icon">
              <img src="./assets/data-cell-ic.svg" alt="Locked Icon" width="12" height="12" />
            </span>
            <span class="listing-status-number">132</span>
          </div>
          <span class="listing-status-label">LCK</span>
        </div>
        <span class="listing-status-divider"><svg width="1" height="35" viewBox="0 0 1 35" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="35" fill="#606F95" fill-opacity="0.1"/></svg></span>
        <div class="listing-status-card" data-tooltip="Timed Out">
          <div class="listing-status-top">
            <span class="listing-status-icon">
              <img src="./assets/data-cell-ic.svg" alt="Timed Out Icon" width="12" height="12" />
            </span>
            <span class="listing-status-number">132</span>
          </div>
          <span class="listing-status-label">T.OUT</span>
        </div>
        <span class="listing-status-divider"><svg width="1" height="35" viewBox="0 0 1 35" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="35" fill="#606F95" fill-opacity="0.1"/></svg></span>
        <div class="listing-status-card" data-tooltip="Rejected">
          <div class="listing-status-top">
            <span class="listing-status-icon">
              <img src="./assets/data-cell-ic.svg" alt="Rejected Icon" width="12" height="12" />
            </span>
            <span class="listing-status-number rejected">43</span>
          </div>
          <span class="listing-status-label">REJ</span>
        </div>
        <span class="listing-status-divider"><svg width="1" height="35" viewBox="0 0 1 35" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="35" fill="#606F95" fill-opacity="0.1"/></svg></span>
        <div class="listing-status-card" data-tooltip="Silent Removals">
          <div class="listing-status-top">
            <span class="listing-status-icon">
              <img src="./assets/data-cell-ic.svg" alt="Silent Removals Icon" width="12" height="12" />
            </span>
            <span class="listing-status-number rejected">54</span>
          </div>
          <span class="listing-status-label">S.REM</span>
        </div>
      </div>
    </div>
    <!-- Ad Spend Section (Figma-accurate, matches screenshot & requirements) -->
    <div class="ad-spend">
      <div class="ad-spend-header-row">
        <div class="ad-spend-header-left">
          <img src="./assets/ad-spend-ic.svg" alt="ADS" class="ad-spend-ads-pill" width="18" height="18" />
          <span class="ad-spend-today-label">Today's Ad Spend</span>
        </div>
        <div class="ad-spend-header-right-group">
          <div class="ad-spend-header-center">
            <span class="ad-spend-header-metric-group">
              <span class="ad-spend-header-label">Total Ad Spend:</span>
              <span class="ad-spend-header-value">$12,237.92</span>
            </span>
            <span class="ad-spend-header-divider"></span>
            <span class="ad-spend-header-metric-group">
              <span class="ad-spend-header-label">Total Orders:</span>
              <span class="ad-spend-header-value">66</span>
            </span>
          </div>
          <div class="ad-spend-header-right">
            <button class="ad-spend-nav-btn ad-spend-prev" aria-label="Previous">
              <img src="./assets/prev-ic.svg" alt="Previous" width="22" height="22" />
            </button>
            <button class="ad-spend-nav-btn ad-spend-next" aria-label="Next">
              <img src="./assets/next-ic.svg" alt="Next" width="22" height="22" />
            </button>
          </div>
        </div>
      </div>
      <div class="ad-spend-marketplaces-row">
        <div class="ad-spend-marketplace-col ad-spend-marketplace-col-us">
          <img src="./assets/US.svg" alt="US" class="ad-spend-flag" width="20" height="20" />
          <div class="ad-spend-value ad-spend-currency">$403,899</div>
          <div class="ad-spend-orders-row">
            <span class="ad-spend-orders">(13)</span>
            <img src="./assets/ACOS-low-ic.svg" alt="ACOS" class="ad-spend-acos-pill" width="16" height="16" />
            <span class="ad-spend-acos-value">9.956</span>
          </div>
        </div>
        <div class="ad-spend-marketplace-divider"></div>
        <div class="ad-spend-marketplace-col ad-spend-marketplace-col-uk">
          <img src="./assets/UK.svg" alt="UK" class="ad-spend-flag" width="20" height="20" />
          <div class="ad-spend-value ad-spend-currency">\xA33,899</div>
          <div class="ad-spend-orders-row">
            <span class="ad-spend-orders">(13)</span>
            <img src="./assets/ACOS-low-ic.svg" alt="ACOS" class="ad-spend-acos-pill" width="16" height="16" />
            <span class="ad-spend-acos-value">9.956</span>
          </div>
        </div>
        <div class="ad-spend-marketplace-divider"></div>
        <div class="ad-spend-marketplace-col ad-spend-marketplace-col-de">
          <img src="./assets/DE.svg" alt="DE" class="ad-spend-flag" width="20" height="20" />
          <div class="ad-spend-value ad-spend-currency">\u20AC32.89</div>
          <div class="ad-spend-orders-row">
            <span class="ad-spend-orders">(22)</span>
            <img src="./assets/ACOS-low-ic.svg" alt="ACOS" class="ad-spend-acos-pill" width="16" height="16" />
            <span class="ad-spend-acos-value">9.956</span>
          </div>
        </div>
        <div class="ad-spend-marketplace-divider"></div>
        <div class="ad-spend-marketplace-col ad-spend-marketplace-col-fr">
          <img src="./assets/FR.svg" alt="FR" class="ad-spend-flag" width="20" height="20" />
          <div class="ad-spend-value ad-spend-currency">\u20AC3.99</div>
          <div class="ad-spend-orders-row">
            <span class="ad-spend-orders">(1)</span>
            <img src="./assets/ACOS-high-ic.svg" alt="ACOS" class="ad-spend-acos-pill" width="16" height="16" />
            <span class="ad-spend-acos-value">30.956</span>
          </div>
        </div>
        <div class="ad-spend-marketplace-divider"></div>
        <div class="ad-spend-marketplace-col ad-spend-marketplace-col-it">
          <img src="./assets/IT.svg" alt="IT" class="ad-spend-flag" width="20" height="20" />
          <div class="ad-spend-value ad-spend-currency">\u20AC11.89</div>
          <div class="ad-spend-orders-row">
            <span class="ad-spend-orders">(0)</span>
          </div>
        </div>
        <div class="ad-spend-marketplace-divider"></div>
        <div class="ad-spend-marketplace-col ad-spend-marketplace-col-es">
          <img src="./assets/ES.svg" alt="ES" class="ad-spend-flag" width="20" height="20" />
          <div class="ad-spend-value ad-spend-currency">\u20AC3.99</div>
          <div class="ad-spend-orders-row">
            <span class="ad-spend-orders">(7)</span>
            <img src="./assets/ACOS-low-ic.svg" alt="ACOS" class="ad-spend-acos-pill" width="16" height="16" />
            <span class="ad-spend-acos-value">7.956</span>
          </div>
        </div>
        <div class="ad-spend-marketplace-divider"></div>
        <div class="ad-spend-marketplace-col ad-spend-marketplace-col-jp">
          <img src="./assets/JP.svg" alt="JP" class="ad-spend-flag" width="20" height="20" />
          <div class="ad-spend-value ad-spend-currency">\xA53.99</div>
          <div class="ad-spend-orders-row">
            <span class="ad-spend-orders">(0)</span>
          </div>
        </div>
      </div>
    </div>
    <!-- End Ad Spend Section -->
    <!-- Sales Cards Section -->
    <div class="sales-cards-container">
  <!-- First row: Today's and Yesterday's Sales Cards -->
  <div class="sales-cards-row">
    <!-- Today's Sales Card -->
    <div class="todays-sales-card-div">
        <div class="Sales-title-date-div">
          <div class="title-date-section">
            <img src="./assets/analytics-sales-ic.svg" alt="Sales Analytics Icon" class="sales-card-icon" width="16" height="16" />
            <div class="title-date-text">
              <span class="sales-card-title">Today's Sales</span>
              <span class="sales-card-date" id="todays-sales-date">June 4, 2025</span>
            </div>
          </div>
        </div>
      <!-- Figma-accurate sales-analytics-div -->
      <div class="sales-analytics-div">
        <div class="sales-count-div">
                        <span class="sales-count zero">0</span>
        </div>
        <div class="analytics-div">
          <div class="metric-col royalties-metric">
            <div class="metric-top">
              <img src="./assets/data-cell-ic.svg" alt="Royalties Icon" class="metric-icon" width="12" height="12" />
                              <span class="metric-value royalties zero">$0.00</span>
            </div>
            <span class="metric-label">Royalties</span>
          </div>
          <div class="metric-divider"></div>
          <div class="metric-col returned-metric">
            <div class="metric-top">
              <img src="./assets/data-cell-ic.svg" alt="Returned Icon" class="metric-icon" width="12" height="12" />
              <span class="metric-value returned zero">0</span>
            </div>
            <div class="metric-label-row">
              <span class="metric-label">Returned</span>
              <span class="metric-percentage returned-percentage zero" style="display: none;">0.0%</span>
            </div>
          </div>
          <div class="metric-divider"></div>
          <div class="metric-col cancelled-metric">
            <div class="metric-top">
              <img src="./assets/data-cell-ic.svg" alt="Cancelled Icon" class="metric-icon" width="12" height="12" />
              <span class="metric-value cancelled zero">0</span>
            </div>
            <span class="metric-label">Cancelled</span>
          </div>
          <div class="metric-divider"></div>
          <div class="metric-col new-metric">
            <div class="metric-top">
              <img src="./assets/data-cell-ic.svg" alt="New Icon" class="metric-icon" width="12" height="12" />
              <span class="metric-value new zero">0</span>
            </div>
            <div class="metric-label-row">
              <span class="metric-label">New</span>
              <span class="metric-percentage new-percentage zero" style="display: none;">0.0%</span>
            </div>
          </div>
          <div class="metric-divider"></div>
          <div class="metric-col ads-metric">
            <div class="metric-top">
              <img src="./assets/data-cell-ic.svg" alt="Ads Icon" class="metric-icon" width="12" height="12" />
              <span class="metric-value ads zero">0</span>
            </div>
            <div class="metric-label-row">
              <span class="metric-label">Ads</span>
              <span class="metric-percentage ads-percentage zero" style="display: none;">0.0%</span>
            </div>
          </div>
        </div>
      </div>
      <hr class="sales-section-divider" />
      <div class="sales-scrollable-content">
        <div class="marketplaces-div">
              <div class="marketplaces-sales-row">
          <div class="marketplace-col all-marketplaces" data-tooltip="All Marketplaces">
            <img src="./assets/all-marketplaces-active-ic.svg" alt="All Marketplaces Active" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">$0.00</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
          <div class="marketplace-col us" data-tooltip="United States">
            <img src="./assets/US.svg" alt="US" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">$0.00</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
          <div class="marketplace-col uk" data-tooltip="United Kingdom">
            <img src="./assets/UK.svg" alt="UK" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">\xA30.00</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
          <div class="marketplace-col de" data-tooltip="Germany">
            <img src="./assets/DE.svg" alt="DE" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">\u20AC0.00</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
          <div class="marketplace-col fr" data-tooltip="France">
            <img src="./assets/FR.svg" alt="FR" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">\u20AC0.00</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
          <div class="marketplace-col it" data-tooltip="Italy">
            <img src="./assets/IT.svg" alt="IT" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">\u20AC0.00</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
          <div class="marketplace-col es" data-tooltip="Spain">
            <img src="./assets/ES.svg" alt="ES" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">\u20AC0.00</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
          <div class="marketplace-col jp" data-tooltip="Japan">
            <img src="./assets/JP.svg" alt="JP" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">\xA50</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
        </div>
      </div>
      <div class="search-tabs-div">
        <div class="search-div">
          <div class="search-input-wrapper">
            <img src="./assets/search-sales-ic.svg" alt="Search" class="search-sales-icon" width="20" height="20" />
            <input type="text" class="search-input" placeholder="Search for a listing, ASIN" />
            <img src="./assets/close-ic.svg" alt="Clear" class="close-search-icon" width="19" height="19" style="opacity:0;" />
          </div>
        </div>
        <div class="sales-filter-div">
          <div class="sales-filter-tab time-tab active" data-tooltip="Sort by Sold at Time">
            <div class="tab-main">
              <span class="tab-label">Time</span>
            </div>
            <img src="./assets/Descending.svg" alt="Descending" class="tab-sort-icon active" width="14" height="14" />
          </div>
          <div class="sales-filter-tab units-tab" data-tooltip="Sort by Units Sold">
            <div class="tab-main">
              <span class="tab-label">Units</span>
            </div>
            <img src="./assets/Descending.svg" alt="Descending" class="tab-sort-icon" width="14" height="14" />
          </div>
          <div class="sales-filter-tab royalties-tab" data-tooltip="Sort by Royalties Earned">
            <div class="tab-main">
              <span class="tab-label">Royalties</span>
            </div>
            <img src="./assets/Descending.svg" alt="Descending" class="tab-sort-icon" width="14" height="14" />
          </div>
          <div class="sales-filter-tab new-tab" data-tooltip="Sort by New Sellers">
            <div class="tab-main">
              <span class="tab-label">New (0)</span>
            </div>
            <img src="./assets/Ascending.svg" alt="Ascending" class="tab-sort-icon" width="14" height="14" />
          </div>
          <div class="sales-filter-tab ad-spend-tab" data-tooltip="Sort by Ad Spend">
            <div class="tab-main">
              <span class="tab-label">Ad Spend</span>
            </div>
            <img src="./assets/Descending.svg" alt="Descending" class="tab-sort-icon" width="14" height="14" />
          </div>
        </div>
      </div>
      <!-- No Results State (hidden by default) -->
      <div class="search-no-results" style="display: none;">
        <div class="no-results-content">
          <img src="./assets/no-results-img.svg" alt="No Results" class="no-results-image" width="52" height="52" />
          <div class="no-results-text">
            <span class="no-results-message">No results found for "<span class="search-term-display"></span>"</span>
          </div>
        </div>
      </div>
      <!-- No Sales State (hidden by default) -->
      <div class="no-sales-state" style="display: none;">
        <img src="./assets/no-sales-img.svg" alt="No Sales" class="no-sales-img" width="98" height="109" />
        <div class="no-sales-text">
          <div class="no-sales-title">No sales here, yet.</div>
          <div class="no-sales-subtitle">Don't be discouraged</div>
        </div>
      </div>
      <!-- US Marketplace - S. T-SHIRT (All fits) -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#FF6B35;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/US.svg" alt="US" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-badge lock-badge" data-tooltip="Product is locked by Amazon"><img src="./assets/locked-listing-ic.svg" alt="Locked" /></span>
            <span class="listing-title">Vintage Music Band Rock Concert Tour Design</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>$89.45</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+8</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-1</span></span>
            <span class="listing-badge fit-types-badge">
              <span class="fit-type male-type"><img src="./assets/male-type-ic.svg" alt="Male" /><span class="type-number">3</span></span>
              <span class="fit-type female-type"><img src="./assets/female-type-ic.svg" alt="Female" /><span class="type-number">2</span></span>
              <span class="fit-type unisex-type"><img src="./assets/unisex-type-ic.svg" alt="Unisex" /><span class="type-number">2</span></span>
              <span class="fit-type youth-type"><img src="./assets/youth-type-ic.svg" alt="Youth" /><span class="type-number">1</span></span>
              <span class="fit-type girls-type"><img src="./assets/girls-type-ic.svg" alt="Girls" /><span class="type-number">0</span></span>
            </span>
            <span class="listing-badge ordered-colors-badge">
              <img src="./assets/ordered-colors-ic.svg" alt="Ordered Colors" />
              <div class="color-circles">
                <div class="color-item">
                  <div class="color-circle" style="background-color: #000000;"></div>
                  <span class="color-number">3</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #FFFFFF;"></div>
                  <span class="color-number">2</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #FF0000;"></div>
                  <span class="color-number">2</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #0066CC;"></div>
                  <span class="color-number">1</span>
                </div>
              </div>
            </span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0ST12345A</span></span>
            <span class="listing-badge BSR-badge" data-tooltip="Best Sellers Rank"><span>#89,234</span></span>
            <span class="listing-badge hot-seller-badge" data-tooltip="High sales in first 30 days."><img src="./assets/hot-seller-ic.svg" alt="Hot Seller" /><span>Hot Seller</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>8:45 AM</span></span>
            <span class="listing-badge Rating-badge" data-tooltip="Customer Rating"><img src="./assets/rating-ic.svg" alt="Rating" /><span>4.7 (156)</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">S. T-Shirt</span>
            <img src="./assets/s-t-shirt-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">$19.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">$9.0 (6)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- UK Marketplace - P. T-SHIRT (Men, Women, Youth) -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#8E44AD;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/UK.svg" alt="UK" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-title">Funny Cat Design Premium Quality Cotton</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>\xA367.23</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+6</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-0</span></span>
            <span class="listing-badge fit-types-badge">
              <span class="fit-type male-type"><img src="./assets/male-type-ic.svg" alt="Male" /><span class="type-number">3</span></span>
              <span class="fit-type female-type"><img src="./assets/female-type-ic.svg" alt="Female" /><span class="type-number">2</span></span>
              <span class="fit-type youth-type"><img src="./assets/youth-type-ic.svg" alt="Youth" /><span class="type-number">1</span></span>
            </span>
            <span class="listing-badge ordered-colors-badge">
              <img src="./assets/ordered-colors-ic.svg" alt="Ordered Colors" />
              <div class="color-circles">
                <div class="color-item">
                  <div class="color-circle" style="background-color: #0066CC;"></div>
                  <span class="color-number">4</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #00AA00;"></div>
                  <span class="color-number">2</span>
                </div>
              </div>
            </span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0PT12345B</span></span>
            <span class="listing-badge BSR-badge" data-tooltip="Best Sellers Rank"><span>#156,789</span></span>
            <span class="listing-badge top-seller-badge" data-tooltip="High sales in first 45 days."><img src="./assets/top-seller-ic.svg" alt="Top Seller" /><span>Top Seller</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>2:30 PM</span></span>
            <span class="listing-badge Rating-badge" data-tooltip="Customer Rating"><img src="./assets/rating-ic.svg" alt="Rating" /><span>4.5 (89)</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">P. T-Shirt</span>
            <img src="./assets/p-t-thirt-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">\xA318.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">\xA33.0 (2)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- DE Marketplace - V-NECK (Women only) -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#2ECC71;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/DE.svg" alt="DE" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-badge lock-badge" data-tooltip="Product is locked by Amazon"><img src="./assets/locked-listing-ic.svg" alt="Locked" /></span>
            <span class="listing-title">Cool Dog Graphic V-Neck Premium Comfort</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>\u20AC45.67</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+4</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-0</span></span>
            <span class="listing-badge fit-types-badge">
              <span class="fit-type female-type"><img src="./assets/female-type-ic.svg" alt="Female" /><span class="type-number">4</span></span>
            </span>
            <span class="listing-badge ordered-colors-badge">
              <img src="./assets/ordered-colors-ic.svg" alt="Ordered Colors" />
              <div class="color-circles">
                <div class="color-item">
                  <div class="color-circle" style="background-color: #FF69B4;"></div>
                  <span class="color-number">2</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #FFFFFF;"></div>
                  <span class="color-number">1</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #000000;"></div>
                  <span class="color-number">1</span>
                </div>
              </div>
            </span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0VN12345C</span></span>
            <span class="listing-badge BSR-badge" data-tooltip="Best Sellers Rank"><span>#234,567</span></span>
            <span class="listing-badge fresh-seller-badge" data-tooltip="Started selling in first 7 days."><img src="./assets/fresh-seller-ic.svg" alt="Fresh Seller" /><span>Fresh Seller</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>11:15 AM</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">V-Neck</span>
            <img src="./assets/v-neck-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">\u20AC21.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">\u20AC4.5 (3)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- IT Marketplace - TANK TOP (Men, Women) -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#E74C3C;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/IT.svg" alt="IT" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-title">Summer Beach Vibes Tank Top Design</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>\u20AC23.45</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+3</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-1</span></span>
            <span class="listing-badge fit-types-badge">
              <span class="fit-type male-type"><img src="./assets/male-type-ic.svg" alt="Male" /><span class="type-number">2</span></span>
              <span class="fit-type female-type"><img src="./assets/female-type-ic.svg" alt="Female" /><span class="type-number">1</span></span>
            </span>
            <span class="listing-badge ordered-colors-badge">
              <img src="./assets/ordered-colors-ic.svg" alt="Ordered Colors" />
              <div class="color-circles">
                <div class="color-item">
                  <div class="color-circle" style="background-color: #FF4500;"></div>
                  <span class="color-number">2</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #87CEEB;"></div>
                  <span class="color-number">1</span>
                </div>
              </div>
            </span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0TK12345D</span></span>
            <span class="listing-badge instant-seller-badge" data-tooltip="Sold in the first 3 days."><img src="./assets/instant-seller-ic.svg" alt="Instant Seller" /><span>Instant Seller</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>4:20 PM</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">Tank Top</span>
            <img src="./assets/tank-top-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">\u20AC16.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">\u20AC6.0 (2)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- ES Marketplace - SWEATSHIRT (Unisex only) -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#F39C12;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/ES.svg" alt="ES" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-title">Cozy Winter Sweatshirt Warm Design</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>\u20AC18.90</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+2</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-0</span></span>
            <span class="listing-badge fit-types-badge">
              <span class="fit-type unisex-type"><img src="./assets/unisex-type-ic.svg" alt="Unisex" /><span class="type-number">2</span></span>
            </span>
            <span class="listing-badge ordered-colors-badge">
              <img src="./assets/ordered-colors-ic.svg" alt="Ordered Colors" />
              <div class="color-circles">
                <div class="color-item">
                  <div class="color-circle" style="background-color: #8B4513;"></div>
                  <span class="color-number">1</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #000000;"></div>
                  <span class="color-number">1</span>
                </div>
              </div>
            </span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0SW12345E</span></span>
            <span class="listing-badge blazing-seller-badge" data-tooltip="Extremely high sales in first 7 days."><img src="./assets/blazing-seller-ic.svg" alt="Blazing Seller" /><span>Blazing Seller</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>7:10 PM</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">Sweatshirt</span>
            <img src="./assets/sweatshirt-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">\u20AC24.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">\u20AC3.0 (2)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- FR Marketplace - P. HOODIE (Unisex only) -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#34495E;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/FR.svg" alt="FR" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-title">Vintage Music Band Rock Concert Tour Premium Hoodie</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>\u20AC32.45</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+3</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-0</span></span>
            <span class="listing-badge fit-types-badge">
              <span class="fit-type unisex-type"><img src="./assets/unisex-type-ic.svg" alt="Unisex" /><span class="type-number">3</span></span>
            </span>
            <span class="listing-badge ordered-colors-badge">
              <img src="./assets/ordered-colors-ic.svg" alt="Ordered Colors" />
              <div class="color-circles">
                <div class="color-item">
                  <div class="color-circle" style="background-color: #000000;"></div>
                  <span class="color-number">2</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #4A4A4A;"></div>
                  <span class="color-number">1</span>
                </div>
              </div>
            </span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0PH12345G</span></span>
            <span class="listing-badge BSR-badge" data-tooltip="Best Sellers Rank"><span>#45,123</span></span>
            <span class="listing-badge instant-seller-badge" data-tooltip="High sales immediately."><img src="./assets/instant-seller-ic.svg" alt="Instant Seller" /><span>Instant Seller</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>4:20 PM</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">P. Hoodie</span>
            <img src="./assets/p-hoodie-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">\u20AC34.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">\u20AC0.0 (0)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- JP Marketplace - PHONE CASE (No fit types) -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#9B59B6;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/JP.svg" alt="JP" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-title">Kawaii Anime Character Phone Case</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>\xA51,890</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+2</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-0</span></span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0PC12345F</span></span>
            <span class="listing-badge amazon-choice-badge" data-tooltip="Amazon's Choice"><img src="./assets/amazon-choice-ic.svg" alt="Amazon Choice" /></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>1:25 PM</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">iPhone Case</span>
            <img src="./assets/phone-case-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">\xA52,199</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">\xA50.7 (0)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- US Marketplace - LONG SLEEVE (Unisex only) -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#27AE60;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/US.svg" alt="US" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-title">Autumn Forest Nature Long Sleeve Design</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>$45.67</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+4</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-1</span></span>
            <span class="listing-badge fit-types-badge">
              <span class="fit-type unisex-type"><img src="./assets/unisex-type-ic.svg" alt="Unisex" /><span class="type-number">4</span></span>
            </span>
            <span class="listing-badge ordered-colors-badge">
              <img src="./assets/ordered-colors-ic.svg" alt="Ordered Colors" />
              <div class="color-circles">
                <div class="color-item">
                  <div class="color-circle" style="background-color: #228B22;"></div>
                  <span class="color-number">2</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #8B4513;"></div>
                  <span class="color-number">1</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #000000;"></div>
                  <span class="color-number">1</span>
                </div>
              </div>
            </span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0LS12345H</span></span>
            <span class="listing-badge BSR-badge" data-tooltip="Best Sellers Rank"><span>#78,456</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>9:15 AM</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">Long Sleeve</span>
            <img src="./assets/long-sleeve-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">$24.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">$0.0 (0)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- UK Marketplace - RAGLAN (Men, Women) -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#E67E22;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/UK.svg" alt="UK" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-title">Retro Baseball Style Raglan Design</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>\xA323.89</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+2</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-0</span></span>
            <span class="listing-badge fit-types-badge">
              <span class="fit-type male-type"><img src="./assets/male-type-ic.svg" alt="Male" /><span class="type-number">1</span></span>
              <span class="fit-type female-type"><img src="./assets/female-type-ic.svg" alt="Female" /><span class="type-number">1</span></span>
            </span>
            <span class="listing-badge ordered-colors-badge">
              <img src="./assets/ordered-colors-ic.svg" alt="Ordered Colors" />
              <div class="color-circles">
                <div class="color-item">
                  <div class="color-circle" style="background-color: #FF0000;"></div>
                  <span class="color-number">1</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #0066CC;"></div>
                  <span class="color-number">1</span>
                </div>
              </div>
            </span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0RG12345I</span></span>
            <span class="listing-badge BSR-badge" data-tooltip="Best Sellers Rank"><span>#123,789</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>6:45 PM</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">Raglan</span>
            <img src="./assets/raglan-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">\xA322.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">\xA30.0 (0)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- DE Marketplace - TUMBLER (Colors only, no fit types) -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#1ABC9C;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/DE.svg" alt="DE" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-title">Travel Coffee Tumbler Mountain Design</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>\u20AC15.67</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+1</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-0</span></span>
            <span class="listing-badge ordered-colors-badge">
              <img src="./assets/ordered-colors-ic.svg" alt="Ordered Colors" />
              <div class="color-circles">
                <div class="color-item">
                  <div class="color-circle" style="background-color: #FFFFFF;"></div>
                  <span class="color-number">1</span>
                </div>
              </div>
            </span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0TU12345J</span></span>
            <span class="listing-badge BSR-badge" data-tooltip="Best Sellers Rank"><span>#567,890</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>3:30 PM</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">Tumbler</span>
            <img src="./assets/tumbler-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">\u20AC18.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">\u20AC0.0 (0)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- IT Marketplace - THROW PILLOW (No fit types, no colors) -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#D35400;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/IT.svg" alt="IT" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-title">Cozy Home Decorative Throw Pillow</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>\u20AC12.34</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+1</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-0</span></span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0TP12345K</span></span>
            <span class="listing-badge BSR-badge" data-tooltip="Best Sellers Rank"><span>#234,567</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>5:15 PM</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">Throw Pillow</span>
            <img src="./assets/throw-pillow-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">\u20AC19.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">\u20AC0.0 (0)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- ES Marketplace - TOTE BAG (No fit types, no colors) -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#7F8C8D;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/ES.svg" alt="ES" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-title">Eco-Friendly Canvas Tote Bag Design</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>\u20AC8.45</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+1</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-0</span></span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0TB12345L</span></span>
            <span class="listing-badge BSR-badge" data-tooltip="Best Sellers Rank"><span>#345,678</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>12:45 PM</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">Tote Bag</span>
            <img src="./assets/tote-bag-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">\u20AC14.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">\u20AC0.0 (0)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- FR Marketplace - POPSOCKET (No fit types, no colors) -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#BDC3C7;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/FR.svg" alt="FR" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-title">Trendy Minimalist PopSocket Design</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>\u20AC5.67</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+1</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-0</span></span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0PS12345M</span></span>
            <span class="listing-badge BSR-badge" data-tooltip="Best Sellers Rank"><span>#456,789</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>10:30 AM</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">PopSocket</span>
            <img src="./assets/popsocket-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">\u20AC9.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">\u20AC0.0 (0)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" /></span>
          </div>
        </div>
      </div>
      </div> <!-- Close sales-scrollable-content -->
    </div>
      
    <!-- Yesterday's Sales Card -->
    <div class="yesterdays-sales-card-div">
      <div class="Sales-title-date-div">
        <div class="title-date-section">
          <img src="./assets/analytics-sales-ic.svg" alt="Sales Analytics Icon" class="sales-card-icon" width="16" height="16" />
          <div class="title-date-text">
            <span class="sales-card-title">Yesterday's Sales</span>
            <span class="sales-card-date" id="yesterdays-sales-date">June 3, 2025</span>
          </div>
        </div>
      </div>
      <!-- Figma-accurate sales-analytics-div -->
      <div class="sales-analytics-div">
        <div class="sales-count-div">
          <span class="sales-count zero">0</span>
        </div>
        <div class="analytics-div">
          <div class="metric-col royalties-metric">
            <div class="metric-top">
              <img src="./assets/data-cell-ic.svg" alt="Royalties Icon" class="metric-icon" width="12" height="12" />
                              <span class="metric-value royalties zero">$0.00</span>
            </div>
            <span class="metric-label">Royalties</span>
          </div>
          <div class="metric-divider"></div>
          <div class="metric-col returned-metric">
            <div class="metric-top">
              <img src="./assets/data-cell-ic.svg" alt="Returned Icon" class="metric-icon" width="12" height="12" />
              <span class="metric-value returned zero">0</span>
            </div>
            <div class="metric-label-row">
              <span class="metric-label">Returned</span>
              <span class="metric-percentage returned-percentage zero" style="display: none;">0.0%</span>
            </div>
          </div>
          <div class="metric-divider"></div>
          <div class="metric-col cancelled-metric">
            <div class="metric-top">
              <img src="./assets/data-cell-ic.svg" alt="Cancelled Icon" class="metric-icon" width="12" height="12" />
              <span class="metric-value cancelled zero">0</span>
            </div>
            <span class="metric-label">Cancelled</span>
          </div>
          <div class="metric-divider"></div>
          <div class="metric-col new-metric">
            <div class="metric-top">
              <img src="./assets/data-cell-ic.svg" alt="New Icon" class="metric-icon" width="12" height="12" />
              <span class="metric-value new zero">0</span>
            </div>
            <div class="metric-label-row">
              <span class="metric-label">New</span>
              <span class="metric-percentage new-percentage zero" style="display: none;">0.0%</span>
            </div>
          </div>
          <div class="metric-divider"></div>
          <div class="metric-col ads-metric">
            <div class="metric-top">
              <img src="./assets/data-cell-ic.svg" alt="Ads Icon" class="metric-icon" width="12" height="12" />
              <span class="metric-value ads zero">0</span>
            </div>
            <div class="metric-label-row">
              <span class="metric-label">Ads</span>
              <span class="metric-percentage ads-percentage zero" style="display: none;">0.0%</span>
            </div>
          </div>
        </div>
      </div>
      <hr class="sales-section-divider" />
      <div class="sales-scrollable-content">
        <div class="marketplaces-div">
        <div class="marketplaces-sales-row">
          <div class="marketplace-col all-marketplaces" data-tooltip="All Marketplaces">
            <img src="./assets/all-marketplaces-active-ic.svg" alt="All Marketplaces Active" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">$0.00</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
          <div class="marketplace-col us" data-tooltip="United States">
            <img src="./assets/US.svg" alt="US" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">$0.00</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
          <div class="marketplace-col uk" data-tooltip="United Kingdom">
            <img src="./assets/UK.svg" alt="UK" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">\xA30.00</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
          <div class="marketplace-col de" data-tooltip="Germany">
            <img src="./assets/DE.svg" alt="DE" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">\u20AC0.00</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
          <div class="marketplace-col fr" data-tooltip="France">
            <img src="./assets/FR.svg" alt="FR" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">\u20AC0.00</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
          <div class="marketplace-col it" data-tooltip="Italy">
            <img src="./assets/IT.svg" alt="IT" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">\u20AC0.00</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
          <div class="marketplace-col es" data-tooltip="Spain">
            <img src="./assets/ES.svg" alt="ES" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">\u20AC0.00</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
          <div class="marketplace-col jp" data-tooltip="Japan">
            <img src="./assets/JP.svg" alt="JP" class="marketplace-icon" width="28" height="28" />
            <span class="marketplace-total-sales-count zero">0</span>
            <span class="marketplace-total-earned-royalties zero">\xA50</span>
            <span class="marketplace-total-returned-units zero">(0)</span>
          </div>
        </div>
      </div>
      <div class="search-tabs-div">
        <div class="search-div">
          <div class="search-input-wrapper">
            <img src="./assets/search-sales-ic.svg" alt="Search" class="search-sales-icon" width="20" height="20" />
            <input type="text" class="search-input" placeholder="Search for a listing, ASIN" />
            <img src="./assets/close-ic.svg" alt="Clear" class="close-search-icon" width="19" height="19" style="opacity:0;" />
          </div>
        </div>
        <div class="sales-filter-div">
          <div class="sales-filter-tab units-tab active" data-tooltip="Sort by Units Sold">
            <div class="tab-main">
              <span class="tab-label">Units</span>
            </div>
            <img src="./assets/Descending.svg" alt="Descending" class="tab-sort-icon active" width="14" height="14" />
          </div>
          <div class="sales-filter-tab royalties-tab" data-tooltip="Sort by Royalties Earned">
            <div class="tab-main">
              <span class="tab-label">Royalties</span>
            </div>
            <img src="./assets/Descending.svg" alt="Descending" class="tab-sort-icon" width="14" height="14" />
          </div>
          <div class="sales-filter-tab new-tab" data-tooltip="Sort by New Sellers">
            <div class="tab-main">
              <span class="tab-label">New (0)</span>
            </div>
            <img src="./assets/Ascending.svg" alt="Ascending" class="tab-sort-icon" width="14" height="14" />
          </div>
          <div class="sales-filter-tab ad-spend-tab" data-tooltip="Sort by Ad Spend">
            <div class="tab-main">
              <span class="tab-label">Ad Spend</span>
            </div>
            <img src="./assets/Descending.svg" alt="Descending" class="tab-sort-icon" width="14" height="14" />
          </div>
        </div>
      </div>
      <!-- No Results State (hidden by default) -->
      <div class="search-no-results" style="display: none;">
        <div class="no-results-content">
          <img src="./assets/no-results-img.svg" alt="No Results" class="no-results-image" width="52" height="52" />
          <div class="no-results-text">
            <span class="no-results-message">No results found for "<span class="search-term-display"></span>"</span>
          </div>
        </div>
      </div>
      <!-- No Sales State (hidden by default) -->
      <div class="no-sales-state" style="display: none;">
        <img src="./assets/no-sales-img.svg" alt="No Sales" class="no-sales-img" width="98" height="109" />
        <div class="no-sales-text">
          <div class="no-sales-title">You had no sales.</div>
          <div class="no-sales-subtitle">Don't be discouraged</div>
        </div>
      </div>
      <!-- First Listing Analytics Section -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#8E44AD;"></div>
        </div>
        <div class="listing-middle-div">
                     <div class="listing-title-row">
             <img src="./assets/US.svg" alt="US" class="listing-marketplace-flag" width="20" height="20" />
             <span class="listing-title">Vintage Music Band Rock Concert Tour Premium Hoodie</span>
           </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>$89.12</span></span>
            <span class="listing-badge lost-royalties-badge" data-tooltip="Royalties Lost"><img src="./assets/lost-royalties-badge-ic.svg" alt="Lost Royalties" /><span>-$12.45</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+3</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-1</span></span>
            <span class="listing-badge canceled-units-badge" data-tooltip="Units Canceled"><img src="./assets/canceled-units-ic.svg" alt="Canceled Units" /><span>1</span></span>
            <span class="listing-badge fit-types-badge">
              <span class="fit-type male-type"><img src="./assets/male-type-ic.svg" alt="Male" /><span class="type-number">1</span></span>
              <span class="fit-type female-type"><img src="./assets/female-type-ic.svg" alt="Female" /><span class="type-number">2</span></span>
              <span class="fit-type unisex-type"><img src="./assets/unisex-type-ic.svg" alt="Unisex" /><span class="type-number">0</span></span>
              <span class="fit-type youth-type"><img src="./assets/youth-type-ic.svg" alt="Youth" /><span class="type-number">0</span></span>
              <span class="fit-type girls-type"><img src="./assets/girls-type-ic.svg" alt="Girls" /><span class="type-number">0</span></span>
            </span>
            <span class="listing-badge ordered-colors-badge">
              <img src="./assets/ordered-colors-ic.svg" alt="Ordered Colors" />
              <div class="color-circles">
                <div class="color-item">
                  <div class="color-circle" style="background-color: #000000;"></div>
                  <span class="color-number">5</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #FFFFFF;"></div>
                  <span class="color-number">3</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #FF0000;"></div>
                  <span class="color-number">2</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #0066CC;"></div>
                  <span class="color-number">1</span>
                </div>
              </div>
            </span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0MN78901P</span></span>
            <span class="listing-badge BSR-badge" data-tooltip="Best Sellers Rank"><span>#234,567</span></span>
            <span class="listing-badge last-month-sold-units-badge" data-tooltip="Last Month Sold Units"><img src="./assets/last-month-sold-units-ic.svg" alt="Last Month Sold Units" /><span>1K</span></span>
            <span class="listing-badge published-date-badge" data-tooltip="First Published Date"><img src="./assets/published-date-ic.svg" alt="Published Date" /><span>Jun 15, 2021</span></span>
            <span class="listing-badge amazon-choice-badge" data-tooltip="Amazon's Choice"><img src="./assets/amazon-choice-ic.svg" alt="Amazon Choice" /></span>
            <span class="listing-badge hot-seller-badge" data-tooltip="High sales in first 30 days."><img src="./assets/hot-seller-ic.svg" alt="Hot Seller" /><span>Hot Seller</span></span>
            <span class="listing-badge top-seller-badge" data-tooltip="High sales in first 45 days."><img src="./assets/top-seller-ic.svg" alt="Top Seller" /><span>Top Seller</span></span>
            <span class="listing-badge instant-seller-badge" data-tooltip="Sold in the first 3 days."><img src="./assets/instant-seller-ic.svg" alt="Instant Seller" /><span>Instant Seller</span></span>
            <span class="listing-badge blazing-seller-badge" data-tooltip="Extremely high sales in first 7 days."><img src="./assets/blazing-seller-ic.svg" alt="Blazing Seller" /><span>Blazing Seller</span></span>
            <span class="listing-badge fresh-seller-badge" data-tooltip="Started selling in first 7 days."><img src="./assets/fresh-seller-ic.svg" alt="Fresh Seller" /><span>Fresh Seller</span></span>
            <span class="listing-badge last-sold-badge" data-tooltip="Last Sold Date"><img src="./assets/last-sold-ic.svg" alt="Last Sold" /><span>Jun 15, 2021</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>6:00 AM</span></span>
            <span class="listing-badge competing-lisitngs-badge" data-tooltip="Estimated Competing Listings, Click to search."><img src="./assets/competing-lisitngs-ic.svg" alt="Competing Listings" /><span>343</span></span>
            <span class="listing-badge sale-badge" data-tooltip="Product is on Sale by Amazon"><img src="./assets/sale-ic.svg" alt="Sale" /><span>Sale (15.99)</span></span>
            <span class="listing-badge total-sold-returned-badge" data-tooltip="All Time Sold/Returned Units"><img src="./assets/total-sold-returned-ic.svg" alt="Total Sold/Returned" /><span>12,908 (-178)</span></span>
            <span class="listing-badge Rating-badge" data-tooltip="Customer Rating"><img src="./assets/rating-ic.svg" alt="Rating" /><span>4.2 (89)</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">P. Hoodie</span>
            <img src="./assets/p-hoodie-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">$39.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">$4.0 (2)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- Second Listing Analytics Section -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#E9EBF2;"></div>
        </div>
        <div class="listing-middle-div">
                     <div class="listing-title-row">
             <span data-tooltip="Sold for the First Time" class="new-seller-badge"><img src="./assets/new-seller-ic.svg" alt="New Seller" class="listing-badge-ic" width="20" height="20" /></span>
             <img src="./assets/FR.svg" alt="FR" class="listing-marketplace-flag" width="20" height="20" />
             <span class="listing-title">Motivational Quote Inspirational Tank Top Design</span>
           </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>$67.89</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+2</span></span>
            <span class="listing-badge canceled-units-badge" data-tooltip="Units Canceled"><img src="./assets/canceled-units-ic.svg" alt="Canceled Units" /><span>1</span></span>
            <span class="listing-badge fit-types-badge">
              <span class="fit-type male-type"><img src="./assets/male-type-ic.svg" alt="Male" /><span class="type-number">1</span></span>
              <span class="fit-type female-type"><img src="./assets/female-type-ic.svg" alt="Female" /><span class="type-number">1</span></span>
              <span class="fit-type unisex-type"><img src="./assets/unisex-type-ic.svg" alt="Unisex" /><span class="type-number">0</span></span>
              <span class="fit-type youth-type"><img src="./assets/youth-type-ic.svg" alt="Youth" /><span class="type-number">0</span></span>
              <span class="fit-type girls-type"><img src="./assets/girls-type-ic.svg" alt="Girls" /><span class="type-number">0</span></span>
            </span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0QR45678S</span></span>
            <span class="listing-badge published-date-badge" data-tooltip="First Published Date"><img src="./assets/published-date-ic.svg" alt="Published Date" /><span>May 10, 2021</span></span>
            <span class="listing-badge blazing-seller-badge" data-tooltip="Extremely high sales in first 7 days."><img src="./assets/blazing-seller-ic.svg" alt="Blazing Seller" /><span>Blazing Seller</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>3:45 PM</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">Tank Top</span>
            <img src="./assets/tank-top-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">$19.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">$2.8 (2)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- Third Listing Analytics Section -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#8A2BE2;"></div>
        </div>
        <div class="listing-middle-div">
                     <div class="listing-title-row">
             <img src="./assets/UK.svg" alt="UK" class="listing-marketplace-flag" width="20" height="20" />
             <span class="listing-title">Modern Abstract Art Design Phone Case</span>
           </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>$45.67</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+6</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-0</span></span>
            <span class="listing-badge fit-types-badge">
              <span class="fit-type male-type"><img src="./assets/male-type-ic.svg" alt="Male" /><span class="type-number">2</span></span>
              <span class="fit-type female-type"><img src="./assets/female-type-ic.svg" alt="Female" /><span class="type-number">4</span></span>
              <span class="fit-type unisex-type"><img src="./assets/unisex-type-ic.svg" alt="Unisex" /><span class="type-number">0</span></span>
              <span class="fit-type youth-type"><img src="./assets/youth-type-ic.svg" alt="Youth" /><span class="type-number">0</span></span>
              <span class="fit-type girls-type"><img src="./assets/girls-type-ic.svg" alt="Girls" /><span class="type-number">0</span></span>
            </span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0TU90123V</span></span>
            <span class="listing-badge published-date-badge" data-tooltip="First Published Date"><img src="./assets/published-date-ic.svg" alt="Published Date" /><span>Jul 8, 2021</span></span>
            <span class="listing-badge instant-seller-badge" data-tooltip="Sold in the first 3 days."><img src="./assets/instant-seller-ic.svg" alt="Instant Seller" /><span>Instant Seller</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>9:20 AM</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-price-only">
            <span class="listing-product-price">$24.99</span>
          </div>
          <div class="listing-product-type-row">
            <span class="listing-product-type">Phone Case</span>
            <img src="./assets/phone-case-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- Fourth Listing Analytics Section - Germany -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#FF6B35;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/DE.svg" alt="DE" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-title">German Oktoberfest Traditional Design Sweatshirt</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>\u20AC34.56</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+3</span></span>
            <span class="listing-badge returned-units-badge" data-tooltip="Units Returned"><img src="./assets/returned-units-ic.svg" alt="Returned Units" /><span>-1</span></span>
            <span class="listing-badge fit-types-badge">
              <span class="fit-type male-type"><img src="./assets/male-type-ic.svg" alt="Male" /><span class="type-number">2</span></span>
              <span class="fit-type female-type"><img src="./assets/female-type-ic.svg" alt="Female" /><span class="type-number">1</span></span>
              <span class="fit-type unisex-type"><img src="./assets/unisex-type-ic.svg" alt="Unisex" /><span class="type-number">0</span></span>
              <span class="fit-type youth-type"><img src="./assets/youth-type-ic.svg" alt="Youth" /><span class="type-number">0</span></span>
              <span class="fit-type girls-type"><img src="./assets/girls-type-ic.svg" alt="Girls" /><span class="type-number">0</span></span>
            </span>
            <span class="listing-badge ordered-colors-badge">
              <img src="./assets/ordered-colors-ic.svg" alt="Ordered Colors" />
              <div class="color-circles">
                <div class="color-item">
                  <div class="color-circle" style="background-color: #FF6B35;"></div>
                  <span class="color-number">2</span>
                </div>
                <div class="color-item">
                  <div class="color-circle" style="background-color: #8B4513;"></div>
                  <span class="color-number">1</span>
                </div>
              </div>
            </span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0DE78901G</span></span>
            <span class="listing-badge fresh-seller-badge" data-tooltip="Started selling in first 7 days."><img src="./assets/fresh-seller-ic.svg" alt="Fresh Seller" /><span>Fresh Seller</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>5:15 PM</span></span>
            <span class="listing-badge Rating-badge" data-tooltip="Customer Rating"><img src="./assets/rating-ic.svg" alt="Rating" /><span>4.5 (156)</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">Sweatshirt</span>
            <img src="./assets/sweatshirt-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">\u20AC29.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">\u20AC3.0 (1)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- Fifth Listing Analytics Section - Italy -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#9B59B6;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/IT.svg" alt="IT" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-title">Italian Renaissance Art Vintage Tote Bag</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>\u20AC18.90</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+2</span></span>
            <span class="listing-badge fit-types-badge">
              <span class="fit-type male-type"><img src="./assets/male-type-ic.svg" alt="Male" /><span class="type-number">0</span></span>
              <span class="fit-type female-type"><img src="./assets/female-type-ic.svg" alt="Female" /><span class="type-number">2</span></span>
              <span class="fit-type unisex-type"><img src="./assets/unisex-type-ic.svg" alt="Unisex" /><span class="type-number">0</span></span>
              <span class="fit-type youth-type"><img src="./assets/youth-type-ic.svg" alt="Youth" /><span class="type-number">0</span></span>
            </span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0IT56789B</span></span>
            <span class="listing-badge published-date-badge" data-tooltip="First Published Date"><img src="./assets/published-date-ic.svg" alt="Published Date" /><span>Apr 22, 2021</span></span>
            <span class="listing-badge instant-seller-badge" data-tooltip="Sold in the first 3 days."><img src="./assets/instant-seller-ic.svg" alt="Instant Seller" /><span>Instant Seller</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>12:30 PM</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-type-row">
            <span class="listing-product-type">Tote Bag</span>
            <img src="./assets/tote-bag-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
            <span class="listing-product-price">\u20AC16.99</span>
          </div>
          <div class="listing-ad-row">
            <img src="./assets/ad-tab-ic.svg" alt="Ad Spend" class="listing-ad-ic" width="20" height="20" />
            <span class="listing-ad-label">\u20AC5.6 (2)</span>
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" /></span>
          </div>
        </div>
      </div>
      
      <!-- Listing Divider -->
      <hr class="listing-section-divider" />
      
      <!-- Sixth Listing Analytics Section - Spain -->
      <div class="listing-analytics-div">
        <div class="listing-left-div state-loaded">
          <div class="listing-product-img" style="background:#E74C3C;"></div>
        </div>
        <div class="listing-middle-div">
          <div class="listing-title-row">
            <img src="./assets/ES.svg" alt="ES" class="listing-marketplace-flag" width="20" height="20" />
            <span class="listing-title">Spanish Flamenco Dance Vintage V-Neck T-Shirt</span>
          </div>
          <div class="listing-badges-row">
            <span class="listing-badge royalties-badge" data-tooltip="Royalties Earned"><img src="./assets/royalties-badge-ic.svg" alt="Royalties" /><span>\u20AC12.45</span></span>
            <span class="listing-badge order-units-badge" data-tooltip="Units Ordered"><img src="./assets/order-units-ic.svg" alt="Order Units" /><span>+1</span></span>
            <span class="listing-badge fit-types-badge">
              <span class="fit-type male-type"><img src="./assets/male-type-ic.svg" alt="Male" /><span class="type-number">0</span></span>
              <span class="fit-type female-type"><img src="./assets/female-type-ic.svg" alt="Female" /><span class="type-number">1</span></span>
              <span class="fit-type unisex-type"><img src="./assets/unisex-type-ic.svg" alt="Unisex" /><span class="type-number">0</span></span>
              <span class="fit-type youth-type"><img src="./assets/youth-type-ic.svg" alt="Youth" /><span class="type-number">0</span></span>
              <span class="fit-type girls-type"><img src="./assets/girls-type-ic.svg" alt="Girls" /><span class="type-number">0</span></span>
            </span>
            <span class="listing-badge Asin-badge" data-tooltip="ASIN, Click to copy."><img src="./assets/asin-ic.svg" alt="ASIN" /><span>B0ES12345V</span></span>
            <span class="listing-badge published-date-badge" data-tooltip="First Published Date"><img src="./assets/published-date-ic.svg" alt="Published Date" /><span>Mar 18, 2021</span></span>
            <span class="listing-badge hot-seller-badge" data-tooltip="High sales in first 30 days."><img src="./assets/hot-seller-ic.svg" alt="Hot Seller" /><span>Hot Seller</span></span>
            <span class="listing-badge sold-time-badge" data-tooltip="Sold at"><img src="./assets/sold-time-ic.svg" alt="Sold Time" /><span>10:50 AM</span></span>
          </div>
        </div>
        <div class="listing-right-div">
          <div class="listing-product-price-only">
            <span class="listing-product-price">\u20AC18.99</span>
          </div>
          <div class="listing-product-type-row">
            <span class="listing-product-type">V-Neck</span>
            <img src="./assets/v-neck-ic.svg" alt="Product Type" class="listing-product-type-ic" width="20" height="20" />
          </div>
          <div class="listing-edit-analyse-row">
            <span class="listing-analyse-ic"><img src="./assets/analyse-ic.svg" alt="Analyse" width="16" height="16" /></span>
            <span class="listing-edit-ic"><img src="./assets/edit-ic.svg" alt="Edit" width="14" height="14" /></span>
          </div>
        </div>
      </div>
      </div> <!-- Close sales-scrollable-content -->
    </div>
  </div>
    </div>
  </div>
    
    <!-- Last Week's Sales Card (Full Width) -->
    <div class="last-week-sales-card">
      <div class="Sales-title-date-div">
        <div class="title-date-section">
          <img src="./assets/analytics-sales-ic.svg" alt="Sales Analytics Icon" class="sales-card-icon" width="16" height="16" />
          <div class="title-date-text">
            <span class="sales-card-title">Last Week's Sales</span>
            <span class="sales-card-date" id="last-week-sales-date">June 4, 2025 to June 10, 2025</span>
          </div>
        </div>
        <div class="controls-section">
          <div class="show-hide-options-div">
            <div class="show-hide-options-btn" data-tooltip="Show/hide Options">
              <img src="./assets/show-hide-options-ic.svg" alt="Show/hide Options" width="16" height="16" />
            </div>
            <!-- Show/Hide Options Dropdown Menu -->
            <div class="show-hide-options-dropdown" id="last-week-show-hide-dropdown">
              <div class="show-hide-options-dropdown-item" data-option="returns">
                <div class="show-hide-options-checkbox">
                  <img src="./assets/checkbox-ic.svg" alt="Checkbox" width="20" height="20" />
                </div>
                <span class="show-hide-options-dropdown-text">Show Returns</span>
              </div>
              <div class="show-hide-options-dropdown-item" data-option="royalties">
                <div class="show-hide-options-checkbox">
                  <img src="./assets/checkbox-ic.svg" alt="Checkbox" width="20" height="20" />
                </div>
                <span class="show-hide-options-dropdown-text">Show Royalties</span>
              </div>
            </div>
          </div>
          <div class="compare-div">
            <div class="compare-btn" data-tooltip="Compare with Previous Periods">
              <img src="./assets/compare-ic.svg" alt="Compare" width="16" height="16" />
            </div>
            <!-- Compare Dropdown Menu -->
            <div class="compare-dropdown" id="compare-dropdown">
              <div class="compare-dropdown-item" data-value="none">
                <div class="compare-checkbox">
                  <img src="./assets/checkbox-ic.svg" alt="Checkbox" width="20" height="20" />
                </div>
                <span class="compare-dropdown-text">Don't compare</span>
              </div>
              <div class="compare-dropdown-item" data-value="week">
                <div class="compare-checkbox">
                  <img src="./assets/uncheckedbox-ic.svg" alt="Checkbox" width="20" height="20" />
                </div>
                <span class="compare-dropdown-text">Compare with previous week</span>
              </div>
              <div class="compare-dropdown-item" data-value="month">
                <div class="compare-checkbox">
                  <img src="./assets/uncheckedbox-ic.svg" alt="Checkbox" width="20" height="20" />
                </div>
                <span class="compare-dropdown-text">Compare with previous month</span>
              </div>
              <div class="compare-dropdown-item" data-value="year">
                <div class="compare-checkbox">
                  <img src="./assets/uncheckedbox-ic.svg" alt="Checkbox" width="20" height="20" />
                </div>
                <span class="compare-dropdown-text">Compare with previous year</span>
              </div>
            </div>
          </div>
          <div class="view-insights-btn">
            <span>View Insights</span>
            <img src="./assets/view-insights-ic.svg" alt="View Insights" width="8" height="8" />
          </div>
        </div>
      </div>
      
      <!-- Chart Container -->
      <div id="last-week-chart-container" class="last-week-chart-container">
        <!-- Chart Skeleton Placeholder -->
        <div class="chart-skeleton">
          <div class="chart-skeleton-header"></div>
          <div class="chart-skeleton-bars">
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Four New Sales Cards Section -->
    <div class="four-sales-cards-section">
      <!-- First row: Current Month and Last Month -->
      <div class="sales-cards-row">
        <!-- Current Month Sales Card -->
        <div class="current-month-card-div">
          <div class="Sales-title-date-div">
            <div class="title-date-section">
              <img src="./assets/analytics-sales-ic.svg" alt="Sales Analytics Icon" class="sales-card-icon" width="16" height="16" />
              <div class="title-date-text">
                <span class="sales-card-title">Current Month</span>
                <span class="sales-card-date">June 1, 2025 to June 30, 2025</span>
              </div>
            </div>
            <div class="view-insights-btn">
              <span>View Insights</span>
              <img src="./assets/view-insights-ic.svg" alt="View Insights" width="8" height="8" />
            </div>
          </div>
          <!-- Figma-accurate sales-analytics-div -->
          <div class="sales-analytics-div">
            <div class="top-row">
              <div class="sales-count-div">
                <span class="sales-count">7,223</span>
              </div>
              <div class="comparison-container">
                <div class="comparison-content">
                  <img src="./assets/down-per-ic.svg" alt="Down" class="comparison-arrow" width="10" height="6" />
                  <span class="comparison-percentage negative">-1.2%</span>
                </div>
                <span class="comparison-label">Compared to previous period</span>
              </div>
            </div>
            <div class="analytics-div">
              <div class="metric-col royalties-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Royalties Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value royalties">$11,933.0</span>
                </div>
                <span class="metric-label">Royalties</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col returned-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Returned Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value returned negative">(-899)</span>
                </div>
                <div class="metric-label-row">
                  <span class="metric-label">Returned</span>
                  <span class="metric-percentage returned-percentage">14.7%</span>
                </div>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col cancelled-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Cancelled Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value cancelled">0</span>
                </div>
                <span class="metric-label">Cancelled</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col new-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="New Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value new">321</span>
                </div>
                <div class="metric-label-row">
                  <span class="metric-label">New</span>
                  <span class="metric-percentage new-percentage zero" style="display: none;">0.0%</span>
                </div>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col ads-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Ads Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value ads">100</span>
                </div>
                <div class="metric-label-row">
                  <span class="metric-label">Ads</span>
                  <span class="metric-percentage ads-percentage zero" style="display: none;">0.0%</span>
                </div>
              </div>
            </div>
          </div>
          <hr class="sales-section-divider" />
          <div class="marketplaces-div">
            <div class="marketplaces-sales-row">
              <div class="marketplace-col us">
                <img src="./assets/US.svg" alt="US" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">5,453</span>
                <span class="marketplace-total-earned-royalties">$1,933.0</span>
                <span class="marketplace-total-returned-units negative">(-143)</span>
              </div>
              <div class="marketplace-col uk">
                <img src="./assets/UK.svg" alt="UK" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">1,023</span>
                <span class="marketplace-total-earned-royalties">\xA31,109.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col de">
                <img src="./assets/DE.svg" alt="DE" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">772</span>
                <span class="marketplace-total-earned-royalties">\u20AC1,043.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col fr">
                <img src="./assets/FR.svg" alt="FR" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">0</span>
                <span class="marketplace-total-earned-royalties zero">\u20AC0.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col it">
                <img src="./assets/IT.svg" alt="IT" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">342</span>
                <span class="marketplace-total-earned-royalties negative">\u20AC-3.0</span>
                <span class="marketplace-total-returned-units negative">(-344)</span>
              </div>
              <div class="marketplace-col es">
                <img src="./assets/ES.svg" alt="ES" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">132</span>
                <span class="marketplace-total-earned-royalties">\u20AC243.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col jp">
                <img src="./assets/JP.svg" alt="JP" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">543</span>
                <span class="marketplace-total-earned-royalties">\xA5130,100</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Last Month Sales Card -->
        <div class="last-month-card-div">
          <div class="Sales-title-date-div">
            <div class="title-date-section">
              <img src="./assets/analytics-sales-ic.svg" alt="Sales Analytics Icon" class="sales-card-icon" width="16" height="16" />
              <div class="title-date-text">
                <span class="sales-card-title">Last Month</span>
                <span class="sales-card-date">June 1, 2025 to June 30, 2025</span>
              </div>
            </div>
            <div class="view-insights-btn">
              <span>View Insights</span>
              <img src="./assets/view-insights-ic.svg" alt="View Insights" width="8" height="8" />
            </div>
          </div>
          <!-- Figma-accurate sales-analytics-div -->
          <div class="sales-analytics-div">
            <div class="top-row">
              <div class="sales-count-div">
                <span class="sales-count">7,023</span>
              </div>
              <div class="comparison-container">
                <div class="comparison-content">
                  <img src="./assets/up-per-ic.svg" alt="Up" class="comparison-arrow" width="10" height="6" />
                  <span class="comparison-percentage positive">****%</span>
                </div>
                <span class="comparison-label">Compared to previous period</span>
              </div>
            </div>
            <div class="analytics-div">
              <div class="metric-col royalties-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Royalties Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value royalties">$227.92</span>
                </div>
                <span class="metric-label">Royalties</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col returned-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Returned Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value returned negative">(-2)</span>
                </div>
                <div class="metric-label-row">
                  <span class="metric-label">Returned</span>
                  <span class="metric-percentage returned-percentage">14.7%</span>
                </div>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col cancelled-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Cancelled Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value cancelled">0</span>
                </div>
                <span class="metric-label">Cancelled</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col new-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="New Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value new">24</span>
                </div>
                <div class="metric-label-row">
                  <span class="metric-label">New</span>
                  <span class="metric-percentage new-percentage zero" style="display: none;">0.0%</span>
                </div>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col ads-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Ads Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value ads">24</span>
                </div>
                <div class="metric-label-row">
                  <span class="metric-label">Ads</span>
                  <span class="metric-percentage ads-percentage zero" style="display: none;">0.0%</span>
                </div>
              </div>
            </div>
          </div>
          <hr class="sales-section-divider" />
          <div class="marketplaces-div">
            <div class="marketplaces-sales-row">
              <div class="marketplace-col us">
                <img src="./assets/US.svg" alt="US" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">5,453</span>
                <span class="marketplace-total-earned-royalties">$1,933.0</span>
                <span class="marketplace-total-returned-units negative">(-143)</span>
              </div>
              <div class="marketplace-col uk">
                <img src="./assets/UK.svg" alt="UK" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">1,023</span>
                <span class="marketplace-total-earned-royalties">\xA31,109.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col de">
                <img src="./assets/DE.svg" alt="DE" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">772</span>
                <span class="marketplace-total-earned-royalties">\u20AC1,043.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col fr">
                <img src="./assets/FR.svg" alt="FR" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">0</span>
                <span class="marketplace-total-earned-royalties zero">\u20AC0.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col it">
                <img src="./assets/IT.svg" alt="IT" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">342</span>
                <span class="marketplace-total-earned-royalties negative">\u20AC-3.0</span>
                <span class="marketplace-total-returned-units negative">(-344)</span>
              </div>
              <div class="marketplace-col es">
                <img src="./assets/ES.svg" alt="ES" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">132</span>
                <span class="marketplace-total-earned-royalties">\u20AC243.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col jp">
                <img src="./assets/JP.svg" alt="JP" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">543</span>
                <span class="marketplace-total-earned-royalties">\xA5130,100</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Second row: Current Year and Last Year -->
      <div class="sales-cards-row">
        <!-- Current Year Sales Card -->
        <div class="current-year-card-div">
          <div class="Sales-title-date-div">
            <div class="title-date-section">
              <img src="./assets/analytics-sales-ic.svg" alt="Sales Analytics Icon" class="sales-card-icon" width="16" height="16" />
              <div class="title-date-text">
                <span class="sales-card-title">Current Year</span>
                <span class="sales-card-date">January 1, 2025 to June 30, 2025</span>
              </div>
            </div>
            <div class="view-insights-btn">
              <span>View Insights</span>
              <img src="./assets/view-insights-ic.svg" alt="View Insights" width="8" height="8" />
            </div>
          </div>
          <!-- Figma-accurate sales-analytics-div -->
          <div class="sales-analytics-div">
            <div class="top-row">
              <div class="sales-count-div">
                <span class="sales-count">14,223</span>
              </div>
              <div class="comparison-container">
                <div class="comparison-content">
                  <img src="./assets/down-per-ic.svg" alt="Down" class="comparison-arrow" width="10" height="6" />
                  <span class="comparison-percentage negative">-1.2%</span>
                </div>
                <span class="comparison-label">Compared to previous period</span>
              </div>
            </div>
            <div class="analytics-div">
              <div class="metric-col royalties-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Royalties Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value royalties">$31,933.0</span>
                </div>
                <span class="metric-label">Royalties</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col returned-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Returned Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value returned negative">(-3,899)</span>
                </div>
                <div class="metric-label-row">
                  <span class="metric-label">Returned</span>
                  <span class="metric-percentage returned-percentage">14.7%</span>
                </div>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col cancelled-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Cancelled Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value cancelled">432</span>
                </div>
                <span class="metric-label">Cancelled</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col new-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="New Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value new">2,100</span>
                </div>
                <div class="metric-label-row">
                  <span class="metric-label">New</span>
                  <span class="metric-percentage new-percentage zero" style="display: none;">0.0%</span>
                </div>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col ads-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Ads Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value ads">2,100</span>
                </div>
                <div class="metric-label-row">
                  <span class="metric-label">Ads</span>
                  <span class="metric-percentage ads-percentage zero" style="display: none;">0.0%</span>
                </div>
              </div>
            </div>
          </div>
          <hr class="sales-section-divider" />
          <div class="marketplaces-div">
            <div class="marketplaces-sales-row">
              <div class="marketplace-col us">
                <img src="./assets/US.svg" alt="US" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">11,453</span>
                <span class="marketplace-total-earned-royalties">$21,933.0</span>
                <span class="marketplace-total-returned-units negative">(-143)</span>
              </div>
              <div class="marketplace-col uk">
                <img src="./assets/UK.svg" alt="UK" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">2,023</span>
                <span class="marketplace-total-earned-royalties">\xA37,109.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col de">
                <img src="./assets/DE.svg" alt="DE" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">1,772</span>
                <span class="marketplace-total-earned-royalties">\u20AC2,043.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col fr">
                <img src="./assets/FR.svg" alt="FR" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">0</span>
                <span class="marketplace-total-earned-royalties zero">\u20AC0.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col it">
                <img src="./assets/IT.svg" alt="IT" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">942</span>
                <span class="marketplace-total-earned-royalties negative">\u20AC-3.0</span>
                <span class="marketplace-total-returned-units negative">(-344)</span>
              </div>
              <div class="marketplace-col es">
                <img src="./assets/ES.svg" alt="ES" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">832</span>
                <span class="marketplace-total-earned-royalties">\u20AC1,243.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col jp">
                <img src="./assets/JP.svg" alt="JP" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">943</span>
                <span class="marketplace-total-earned-royalties">\xA5130,100</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Last Year Sales Card -->
        <div class="last-year-card-div">
          <div class="Sales-title-date-div">
            <div class="title-date-section">
              <img src="./assets/analytics-sales-ic.svg" alt="Sales Analytics Icon" class="sales-card-icon" width="16" height="16" />
              <div class="title-date-text">
                <span class="sales-card-title">Last Year</span>
                <span class="sales-card-date">January 1, 2024 to December 31, 2024</span>
              </div>
            </div>
            <div class="view-insights-btn">
              <span>View Insights</span>
              <img src="./assets/view-insights-ic.svg" alt="View Insights" width="8" height="8" />
            </div>
          </div>
          <!-- Figma-accurate sales-analytics-div -->
          <div class="sales-analytics-div">
            <div class="top-row">
              <div class="sales-count-div">
                <span class="sales-count">32,023</span>
              </div>
              <div class="comparison-container">
                <div class="comparison-content">
                  <img src="./assets/up-per-ic.svg" alt="Up" class="comparison-arrow" width="10" height="6" />
                  <span class="comparison-percentage positive">****%</span>
                </div>
                <span class="comparison-label">Compared to previous period</span>
              </div>
            </div>
            <div class="analytics-div">
              <div class="metric-col royalties-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Royalties Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value royalties">$31,933.0</span>
                </div>
                <span class="metric-label">Royalties</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col returned-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Returned Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value returned negative">(-3,899)</span>
                </div>
                <div class="metric-label-row">
                  <span class="metric-label">Returned</span>
                  <span class="metric-percentage returned-percentage">14.7%</span>
                </div>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col cancelled-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Cancelled Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value cancelled">432</span>
                </div>
                <span class="metric-label">Cancelled</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col new-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="New Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value new">2,100</span>
                </div>
                <div class="metric-label-row">
                  <span class="metric-label">New</span>
                  <span class="metric-percentage new-percentage zero" style="display: none;">0.0%</span>
                </div>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col ads-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Ads Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value ads">2,100</span>
                </div>
                <div class="metric-label-row">
                  <span class="metric-label">Ads</span>
                  <span class="metric-percentage ads-percentage zero" style="display: none;">0.0%</span>
                </div>
              </div>
            </div>
          </div>
          <hr class="sales-section-divider" />
          <div class="marketplaces-div">
            <div class="marketplaces-sales-row">
              <div class="marketplace-col us">
                <img src="./assets/US.svg" alt="US" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">11,453</span>
                <span class="marketplace-total-earned-royalties">$21,933.0</span>
                <span class="marketplace-total-returned-units negative">(-143)</span>
              </div>
              <div class="marketplace-col uk">
                <img src="./assets/UK.svg" alt="UK" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">2,023</span>
                <span class="marketplace-total-earned-royalties">\xA37,109.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col de">
                <img src="./assets/DE.svg" alt="DE" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">1,772</span>
                <span class="marketplace-total-earned-royalties">\u20AC2,043.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col fr">
                <img src="./assets/FR.svg" alt="FR" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">0</span>
                <span class="marketplace-total-earned-royalties zero">\u20AC0.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col it">
                <img src="./assets/IT.svg" alt="IT" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">942</span>
                <span class="marketplace-total-earned-royalties">\u20AC1,243.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col es">
                <img src="./assets/ES.svg" alt="ES" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">832</span>
                <span class="marketplace-total-earned-royalties">\u20AC1,243.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col jp">
                <img src="./assets/JP.svg" alt="JP" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">943</span>
                <span class="marketplace-total-earned-royalties">\xA5130,100</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Today vs Previous Years Sales Card (Full Width) -->
    <div class="today-vs-previous-years-card">
      <div class="Sales-title-date-div">
        <div class="title-date-section">
          <img src="./assets/analytics-sales-ic.svg" alt="Sales Analytics Icon" class="sales-card-icon" width="16" height="16" />
          <div class="title-date-text">
            <span class="sales-card-title">Today vs Previous Years</span>
            <span class="sales-card-date" id="today-vs-previous-years-date">June 26</span>
          </div>
        </div>
        <div class="controls-section">
          <div class="show-hide-options-div">
            <div class="show-hide-options-btn" data-tooltip="Show/hide Options">
              <img src="./assets/show-hide-options-ic.svg" alt="Show/hide Options" width="16" height="16" />
            </div>
            <!-- Show/Hide Options Dropdown Menu -->
            <div class="show-hide-options-dropdown" id="today-vs-previous-show-hide-dropdown">
              <div class="show-hide-options-dropdown-item" data-option="returns">
                <div class="show-hide-options-checkbox">
                  <img src="./assets/checkbox-ic.svg" alt="Checkbox" width="20" height="20" />
                </div>
                <span class="show-hide-options-dropdown-text">Show Returns</span>
              </div>
              <div class="show-hide-options-dropdown-item" data-option="royalties">
                <div class="show-hide-options-checkbox">
                  <img src="./assets/checkbox-ic.svg" alt="Checkbox" width="20" height="20" />
                </div>
                <span class="show-hide-options-dropdown-text">Show Royalties</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Chart Container -->
      <div id="today-vs-previous-years-chart-container" class="today-vs-previous-years-chart-container">
        <!-- Chart Skeleton Placeholder -->
        <div class="chart-skeleton">
          <div class="chart-skeleton-header"></div>
          <div class="chart-skeleton-bars">
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Monthly Sales Card (Full Width) -->
    <div class="monthly-sales-card">
      <div class="Sales-title-date-div">
        <div class="title-date-section">
          <img src="./assets/analytics-sales-ic.svg" alt="Sales Analytics Icon" class="sales-card-icon" width="16" height="16" />
          <div class="title-date-text">
            <span class="sales-card-title">Monthly Sales</span>
            <span class="sales-card-date" id="monthly-sales-date">Jan to Dec 2025</span>
          </div>
        </div>
        <div class="controls-section">
          <div class="show-hide-options-div">
            <div class="show-hide-options-btn" data-tooltip="Show/hide Options">
              <img src="./assets/show-hide-options-ic.svg" alt="Show/hide Options" width="16" height="16" />
            </div>
            <!-- Show/Hide Options Dropdown Menu -->
            <div class="show-hide-options-dropdown" id="monthly-show-hide-dropdown">
              <div class="show-hide-options-dropdown-item" data-option="returns">
                <div class="show-hide-options-checkbox">
                  <img src="./assets/checkbox-ic.svg" alt="Checkbox" width="20" height="20" />
                </div>
                <span class="show-hide-options-dropdown-text">Show Returns</span>
              </div>
              <div class="show-hide-options-dropdown-item" data-option="royalties">
                <div class="show-hide-options-checkbox">
                  <img src="./assets/checkbox-ic.svg" alt="Checkbox" width="20" height="20" />
                </div>
                <span class="show-hide-options-dropdown-text">Show Royalties</span>
              </div>
            </div>
          </div>
          <div class="compare-div">
            <div class="compare-btn" data-tooltip="Compare with Previous Periods">
              <img src="./assets/compare-ic.svg" alt="Compare" width="16" height="16" />
            </div>
            <!-- Compare Dropdown Menu -->
            <div class="compare-dropdown" id="monthly-compare-dropdown">
              <div class="compare-dropdown-item" data-value="none">
                <div class="compare-checkbox">
                  <img src="./assets/checkbox-ic.svg" alt="Checkbox" width="20" height="20" />
                </div>
                <span class="compare-dropdown-text">Don't compare</span>
              </div>
              <div class="compare-dropdown-item" data-value="previous-year">
                <div class="compare-checkbox">
                  <img src="./assets/uncheckedbox-ic.svg" alt="Checkbox" width="20" height="20" />
                </div>
                <span class="compare-dropdown-text">Compare with previous year</span>
              </div>
            </div>
          </div>
          <!-- Year Dropdown -->
          <div class="monthly-sales-year-dropdown snap-dropdown" id="monthlySalesYearDropdown" data-tooltip="Select Year">
            <div class="dropdown-header" tabindex="0">
              <div class="dropdown-header-content">
                <span class="dropdown-selected-label" style="font-size:12px;vertical-align:middle;position:relative;top:1px;">2025</span>
              </div>
              <img src="./assets/dropdown-ic.svg" alt="Dropdown" class="dropdown-arrow" />
            </div>
            <div class="dropdown-menu hidden" style="height:auto;max-height:200px;overflow-y:auto;box-shadow:none;width:100%;min-width:80px;">
              <div class="dropdown-list" style="max-height:200px;overflow-y:auto;">
                <!-- Year options will be populated by JavaScript -->
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Quarterly Sales Summary (appears for single-year view only) -->
      <div class="quarter-sales-div hidden" id="quarter-sales-div"></div>

      <!-- Monthly Sales Chart Container -->
      <div id="monthly-sales-chart-container" class="monthly-sales-chart-container">
        <!-- Chart Skeleton Placeholder -->
        <div class="chart-skeleton">
          <div class="chart-skeleton-header"></div>
          <div class="chart-skeleton-bars">
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Yearly Sales Card (Full Width) -->
    <div class="yearly-sales-card">
      <div class="Sales-title-date-div">
        <div class="title-date-section">
          <img src="./assets/analytics-sales-ic.svg" alt="Sales Analytics Icon" class="sales-card-icon" width="16" height="16" />
          <div class="title-date-text">
            <span class="sales-card-title">Yearly Sales</span>
            <span class="sales-card-date" id="yearly-sales-date">2000 to 2025</span>
          </div>
        </div>
        <div class="controls-section">
          <div class="show-hide-options-div">
            <div class="show-hide-options-btn" data-tooltip="Show/hide Options">
              <img src="./assets/show-hide-options-ic.svg" alt="Show/hide Options" width="16" height="16" />
            </div>
            <!-- Show/Hide Options Dropdown Menu -->
            <div class="show-hide-options-dropdown" id="yearly-show-hide-dropdown">
              <div class="show-hide-options-dropdown-item" data-option="returns">
                <div class="show-hide-options-checkbox">
                  <img src="./assets/checkbox-ic.svg" alt="Checkbox" width="20" height="20" />
                </div>
                <span class="show-hide-options-dropdown-text">Show Returns</span>
              </div>
              <div class="show-hide-options-dropdown-item" data-option="royalties">
                <div class="show-hide-options-checkbox">
                  <img src="./assets/checkbox-ic.svg" alt="Checkbox" width="20" height="20" />
                </div>
                <span class="show-hide-options-dropdown-text">Show Royalties</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Yearly Sales Chart Container -->
      <div id="yearly-sales-chart-container" class="yearly-sales-chart-container">
        <!-- Chart Skeleton Placeholder -->
        <div class="chart-skeleton">
          <div class="chart-skeleton-header"></div>
          <div class="chart-skeleton-bars">
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
            <div class="chart-skeleton-bar"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Top Four Sales Cards Section -->
    <div class="top-four-sales-cards-section">
      <!-- First row: Top Day and Top Week -->
      <div class="sales-cards-row">
        <!-- Top Day Sales Card -->
        <div class="top-day-card-div">
          <div class="Sales-title-date-div">
            <div class="title-date-section">
              <img src="./assets/analytics-sales-ic.svg" alt="Sales Analytics Icon" class="sales-card-icon" width="16" height="16" />
              <div class="title-date-text">
                <span class="sales-card-title">Top Day</span>
                <span class="sales-card-date">June 15, 2025</span>
              </div>
            </div>
            <div class="view-insights-btn">
              <span>View Insights</span>
              <img src="./assets/view-insights-ic.svg" alt="View Insights" width="8" height="8" />
            </div>
          </div>
          <!-- Figma-accurate sales-analytics-div -->
          <div class="sales-analytics-div">
            <div class="top-row">
              <div class="sales-count-div">
                <span class="sales-count">1,247</span>
              </div>
            </div>
            <div class="analytics-div">
              <div class="metric-col royalties-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Royalties Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value royalties">$2,058.0</span>
                </div>
                <span class="metric-label">Royalties</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col returned-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Returned Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value returned negative">(-23)</span>
                </div>
                <div class="metric-label-row">
                  <span class="metric-label">Returned</span>
                  <span class="metric-percentage returned-percentage">1.8%</span>
                </div>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col cancelled-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Cancelled Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value cancelled">0</span>
                </div>
                <span class="metric-label">Cancelled</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col new-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="New Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value new">89</span>
                </div>
                <div class="metric-label-row">
                  <span class="metric-label">New</span>
                  <span class="metric-percentage new-percentage zero" style="display: none;">0.0%</span>
                </div>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col ads-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Ads Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value ads">34</span>
                </div>
                <div class="metric-label-row">
                  <span class="metric-label">Ads</span>
                  <span class="metric-percentage ads-percentage zero" style="display: none;">0.0%</span>
                </div>
              </div>
            </div>
          </div>
          <hr class="sales-section-divider" />
          <div class="marketplaces-div">
            <div class="marketplaces-sales-row">
              <div class="marketplace-col us">
                <img src="./assets/US.svg" alt="US" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">943</span>
                <span class="marketplace-total-earned-royalties">$1,558.0</span>
                <span class="marketplace-total-returned-units negative">(-18)</span>
              </div>
              <div class="marketplace-col uk">
                <img src="./assets/UK.svg" alt="UK" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">187</span>
                <span class="marketplace-total-earned-royalties">\xA3203.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col de">
                <img src="./assets/DE.svg" alt="DE" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">89</span>
                <span class="marketplace-total-earned-royalties">\u20AC120.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col fr">
                <img src="./assets/FR.svg" alt="FR" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">0</span>
                <span class="marketplace-total-earned-royalties zero">\u20AC0.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col it">
                <img src="./assets/IT.svg" alt="IT" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">28</span>
                <span class="marketplace-total-earned-royalties">\u20AC38.0</span>
                <span class="marketplace-total-returned-units negative">(-5)</span>
              </div>
              <div class="marketplace-col es">
                <img src="./assets/ES.svg" alt="ES" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">0</span>
                <span class="marketplace-total-earned-royalties zero">\u20AC0.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col jp">
                <img src="./assets/JP.svg" alt="JP" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">0</span>
                <span class="marketplace-total-earned-royalties">\xA50</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Top Week Sales Card -->
        <div class="top-week-card-div">
          <div class="Sales-title-date-div">
            <div class="title-date-section">
              <img src="./assets/analytics-sales-ic.svg" alt="Sales Analytics Icon" class="sales-card-icon" width="16" height="16" />
              <div class="title-date-text">
                <span class="sales-card-title">Top Week</span>
                <span class="sales-card-date">June 9, 2025 to June 15, 2025</span>
              </div>
            </div>
            <div class="view-insights-btn">
              <span>View Insights</span>
              <img src="./assets/view-insights-ic.svg" alt="View Insights" width="8" height="8" />
            </div>
          </div>
          <!-- Figma-accurate sales-analytics-div -->
          <div class="sales-analytics-div">
            <div class="top-row">
              <div class="sales-count-div">
                <span class="sales-count">6,834</span>
              </div>
            </div>
            <div class="analytics-div">
              <div class="metric-col royalties-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Royalties Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value royalties">$11,297.0</span>
                </div>
                <span class="metric-label">Royalties</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col returned-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Returned Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value returned negative">(-127)</span>
                </div>
                <div class="metric-label-row">
                  <span class="metric-label">Returned</span>
                  <span class="metric-percentage returned-percentage">1.9%</span>
                </div>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col cancelled-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Cancelled Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value cancelled">0</span>
                </div>
                <span class="metric-label">Cancelled</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col new-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="New Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value new">478</span>
                </div>
                <div class="metric-label-row">
                  <span class="metric-label">New</span>
                  <span class="metric-percentage new-percentage zero" style="display: none;">0.0%</span>
                </div>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col ads-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Ads Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value ads">189</span>
                </div>
                <div class="metric-label-row">
                  <span class="metric-label">Ads</span>
                  <span class="metric-percentage ads-percentage zero" style="display: none;">0.0%</span>
                </div>
              </div>
            </div>
          </div>
          <hr class="sales-section-divider" />
          <div class="marketplaces-div">
            <div class="marketplaces-sales-row">
              <div class="marketplace-col us">
                <img src="./assets/US.svg" alt="US" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">5,167</span>
                <span class="marketplace-total-earned-royalties">$8,542.0</span>
                <span class="marketplace-total-returned-units negative">(-96)</span>
              </div>
              <div class="marketplace-col uk">
                <img src="./assets/UK.svg" alt="UK" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">1,002</span>
                <span class="marketplace-total-earned-royalties">\xA31,087.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col de">
                <img src="./assets/DE.svg" alt="DE" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">489</span>
                <span class="marketplace-total-earned-royalties">\u20AC661.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col fr">
                <img src="./assets/FR.svg" alt="FR" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">0</span>
                <span class="marketplace-total-earned-royalties zero">\u20AC0.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col it">
                <img src="./assets/IT.svg" alt="IT" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">176</span>
                <span class="marketplace-total-earned-royalties">\u20AC238.0</span>
                <span class="marketplace-total-returned-units negative">(-31)</span>
              </div>
              <div class="marketplace-col es">
                <img src="./assets/ES.svg" alt="ES" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">0</span>
                <span class="marketplace-total-earned-royalties zero">\u20AC0.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col jp">
                <img src="./assets/JP.svg" alt="JP" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">0</span>
                <span class="marketplace-total-earned-royalties">\xA50</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Second row: Top Month and Top Year -->
      <div class="sales-cards-row">
        <!-- Top Month Sales Card -->
        <div class="top-month-card-div">
          <div class="Sales-title-date-div">
            <div class="title-date-section">
              <img src="./assets/analytics-sales-ic.svg" alt="Sales Analytics Icon" class="sales-card-icon" width="16" height="16" />
              <div class="title-date-text">
                <span class="sales-card-title">Top Month</span>
                <span class="sales-card-date">March 2025</span>
              </div>
            </div>
            <div class="view-insights-btn">
              <span>View Insights</span>
              <img src="./assets/view-insights-ic.svg" alt="View Insights" width="8" height="8" />
            </div>
          </div>
          <!-- Figma-accurate sales-analytics-div -->
          <div class="sales-analytics-div">
            <div class="top-row">
              <div class="sales-count-div">
                <span class="sales-count">28,947</span>
              </div>
            </div>
            <div class="analytics-div">
              <div class="metric-col royalties-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Royalties Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value royalties">$47,823.0</span>
                </div>
                <span class="metric-label">Royalties</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col returned-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Returned Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value returned negative">(-1,247)</span>
                </div>
                <div class="metric-label-row">
                  <span class="metric-label">Returned</span>
                  <span class="metric-percentage returned-percentage">4.3%</span>
                </div>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col cancelled-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Cancelled Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value cancelled">0</span>
                </div>
                <span class="metric-label">Cancelled</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col new-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="New Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value new">2,034</span>
                </div>
                <div class="metric-label-row">
                  <span class="metric-label">New</span>
                  <span class="metric-percentage new-percentage zero" style="display: none;">0.0%</span>
                </div>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col ads-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Ads Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value ads">802</span>
                </div>
                <div class="metric-label-row">
                  <span class="metric-label">Ads</span>
                  <span class="metric-percentage ads-percentage zero" style="display: none;">0.0%</span>
                </div>
              </div>
            </div>
          </div>
          <hr class="sales-section-divider" />
          <div class="marketplaces-div">
            <div class="marketplaces-sales-row">
              <div class="marketplace-col us">
                <img src="./assets/US.svg" alt="US" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">21,876</span>
                <span class="marketplace-total-earned-royalties">$36,158.0</span>
                <span class="marketplace-total-returned-units negative">(-943)</span>
              </div>
              <div class="marketplace-col uk">
                <img src="./assets/UK.svg" alt="UK" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">4,234</span>
                <span class="marketplace-total-earned-royalties">\xA34,598.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col de">
                <img src="./assets/DE.svg" alt="DE" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">2,067</span>
                <span class="marketplace-total-earned-royalties">\u20AC2,791.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col fr">
                <img src="./assets/FR.svg" alt="FR" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">0</span>
                <span class="marketplace-total-earned-royalties zero">\u20AC0.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col it">
                <img src="./assets/IT.svg" alt="IT" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">770</span>
                <span class="marketplace-total-earned-royalties">\u20AC1,040.0</span>
                <span class="marketplace-total-returned-units negative">(-304)</span>
              </div>
              <div class="marketplace-col es">
                <img src="./assets/ES.svg" alt="ES" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">0</span>
                <span class="marketplace-total-earned-royalties zero">\u20AC0.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col jp">
                <img src="./assets/JP.svg" alt="JP" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">0</span>
                <span class="marketplace-total-earned-royalties">\xA50</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Top Year Sales Card -->
        <div class="top-year-card-div">
          <div class="Sales-title-date-div">
            <div class="title-date-section">
              <img src="./assets/analytics-sales-ic.svg" alt="Sales Analytics Icon" class="sales-card-icon" width="16" height="16" />
              <div class="title-date-text">
                <span class="sales-card-title">Top Year</span>
                <span class="sales-card-date">2024</span>
              </div>
            </div>
            <div class="view-insights-btn">
              <span>View Insights</span>
              <img src="./assets/view-insights-ic.svg" alt="View Insights" width="8" height="8" />
            </div>
          </div>
          <!-- Figma-accurate sales-analytics-div -->
          <div class="sales-analytics-div">
            <div class="top-row">
              <div class="sales-count-div">
                <span class="sales-count">187,234</span>
              </div>
            </div>
            <div class="analytics-div">
              <div class="metric-col royalties-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Royalties Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value royalties">$309,487.0</span>
                </div>
                <span class="metric-label">Royalties</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col returned-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Returned Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value returned negative">(-8,047)</span>
                </div>
                <div class="metric-label-row">
                  <span class="metric-label">Returned</span>
                  <span class="metric-percentage returned-percentage">4.3%</span>
                </div>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col cancelled-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Cancelled Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value cancelled">0</span>
                </div>
                <span class="metric-label">Cancelled</span>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col new-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="New Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value new">13,106</span>
                </div>
                <div class="metric-label-row">
                  <span class="metric-label">New</span>
                  <span class="metric-percentage new-percentage">7.0%</span>
                </div>
              </div>
              <div class="metric-divider"></div>
              <div class="metric-col ads-metric">
                <div class="metric-top">
                  <img src="./assets/data-cell-ic.svg" alt="Ads Icon" class="metric-icon" width="12" height="12" />
                  <span class="metric-value ads">5,242</span>
                </div>
                <div class="metric-label-row">
                  <span class="metric-label">Ads</span>
                  <span class="metric-percentage ads-percentage">2.8%</span>
                </div>
              </div>
            </div>
          </div>
          <hr class="sales-section-divider" />
          <div class="marketplaces-div">
            <div class="marketplaces-sales-row">
              <div class="marketplace-col us">
                <img src="./assets/US.svg" alt="US" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">141,426</span>
                <span class="marketplace-total-earned-royalties">$234,164.0</span>
                <span class="marketplace-total-returned-units negative">(-6,087)</span>
              </div>
              <div class="marketplace-col uk">
                <img src="./assets/UK.svg" alt="UK" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">27,347</span>
                <span class="marketplace-total-earned-royalties">\xA329,687.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col de">
                <img src="./assets/DE.svg" alt="DE" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">13,367</span>
                <span class="marketplace-total-earned-royalties">\u20AC18,047.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col fr">
                <img src="./assets/FR.svg" alt="FR" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">0</span>
                <span class="marketplace-total-earned-royalties zero">\u20AC0.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col it">
                <img src="./assets/IT.svg" alt="IT" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">5,094</span>
                <span class="marketplace-total-earned-royalties">\u20AC6,877.0</span>
                <span class="marketplace-total-returned-units negative">(-1,960)</span>
              </div>
              <div class="marketplace-col es">
                <img src="./assets/ES.svg" alt="ES" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">0</span>
                <span class="marketplace-total-earned-royalties zero">\u20AC0.0</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
              <div class="marketplace-col jp">
                <img src="./assets/JP.svg" alt="JP" class="marketplace-icon" width="28" height="28" />
                <span class="marketplace-total-sales-count">0</span>
                <span class="marketplace-total-earned-royalties">\xA50</span>
                <span class="marketplace-total-returned-units">(0)</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Insights and Feedback Section (below Top Four) -->
      <div class="insights-and-feedback-section">
        <div class="sales-cards-row">
          <!-- Lifetime Insights Card -->
          <div class="lifetime-insights-card-div">
            <div class="Sales-title-date-div">
              <div class="title-date-section">
                <img src="./assets/analytics-sales-ic.svg" alt="Sales Analytics Icon" class="sales-card-icon" width="16" height="16" />
                <div class="title-date-text">
                  <span class="sales-card-title">Lifetime Insights</span>
                </div>
              </div>
              <div class="view-insights-btn">
                <span>View Insights</span>
                <img src="./assets/view-insights-ic.svg" alt="View Insights" width="8" height="8" />
              </div>
            </div>
            <!-- Figma-accurate sales-analytics-div -->
            <div class="sales-analytics-div">
              <div class="top-row">
                <div class="sales-count-div">
                  <span class="sales-count">217,223</span>
                </div>
              </div>
              <div class="analytics-div">
                <div class="metric-col royalties-metric">
                  <div class="metric-top">
                    <img src="./assets/data-cell-ic.svg" alt="Royalties Icon" class="metric-icon" width="12" height="12" />
                    <span class="metric-value royalties">$511,933.0</span>
                  </div>
                  <span class="metric-label">Royalties</span>
                </div>
                <div class="metric-divider"></div>
                <div class="metric-col returned-metric">
                  <div class="metric-top">
                    <img src="./assets/data-cell-ic.svg" alt="Returned Icon" class="metric-icon" width="12" height="12" />
                    <span class="metric-value returned negative">(-17,099)</span>
                  </div>
                  <div class="metric-label-row">
                    <span class="metric-label">Returned</span>
                    <span class="metric-percentage returned-percentage">14.7%</span>
                  </div>
                </div>
                <div class="metric-divider"></div>
                <div class="metric-col cancelled-metric">
                  <div class="metric-top">
                    <img src="./assets/data-cell-ic.svg" alt="Cancelled Icon" class="metric-icon" width="12" height="12" />
                    <span class="metric-value cancelled">2,231</span>
                  </div>
                  <span class="metric-label">Cancelled</span>
                </div>
                <div class="metric-divider"></div>
                <div class="metric-col ads-metric">
                  <div class="metric-top">
                    <img src="./assets/data-cell-ic.svg" alt="Ads Icon" class="metric-icon" width="12" height="12" />
                    <span class="metric-value ads">65,000</span>
                  </div>
                  <div class="metric-label-row">
                    <span class="metric-label">Ads</span>
                    <span class="metric-percentage ads-percentage zero" style="display: none;">0.0%</span>
                  </div>
                </div>
              </div>
            </div>
            <hr class="sales-section-divider" />
            <div class="marketplaces-div">
              <div class="marketplaces-sales-row">
                <div class="marketplace-col us">
                  <img src="./assets/US.svg" alt="US" class="marketplace-icon" width="28" height="28" />
                  <span class="marketplace-total-sales-count">190,153</span>
                  <span class="marketplace-total-earned-royalties">$490,933.0</span>
                  <span class="marketplace-total-returned-units negative">(-11,143)</span>
                </div>
                <div class="marketplace-col uk">
                  <img src="./assets/UK.svg" alt="UK" class="marketplace-icon" width="28" height="28" />
                  <span class="marketplace-total-sales-count">11,223</span>
                  <span class="marketplace-total-earned-royalties">\xA311,409.0</span>
                  <span class="marketplace-total-returned-units zero">(0)</span>
                </div>
                <div class="marketplace-col de">
                  <img src="./assets/DE.svg" alt="DE" class="marketplace-icon" width="28" height="28" />
                  <span class="marketplace-total-sales-count">31,232</span>
                  <span class="marketplace-total-earned-royalties">\u20AC8,021.0</span>
                  <span class="marketplace-total-returned-units zero">(0)</span>
                </div>
                <div class="marketplace-col fr">
                  <img src="./assets/FR.svg" alt="FR" class="marketplace-icon" width="28" height="28" />
                  <span class="marketplace-total-sales-count">1,232</span>
                  <span class="marketplace-total-earned-royalties zero">\u20AC0.0</span>
                  <span class="marketplace-total-returned-units zero">(0)</span>
                </div>
                <div class="marketplace-col it">
                  <img src="./assets/IT.svg" alt="IT" class="marketplace-icon" width="28" height="28" />
                  <span class="marketplace-total-sales-count">21,232</span>
                  <span class="marketplace-total-earned-royalties negative">\u20AC-3.0</span>
                  <span class="marketplace-total-returned-units negative">(-3)</span>
                </div>
                <div class="marketplace-col es">
                  <img src="./assets/ES.svg" alt="ES" class="marketplace-icon" width="28" height="28" />
                  <span class="marketplace-total-sales-count">1,232</span>
                  <span class="marketplace-total-earned-royalties">\u20AC8,021.0</span>
                  <span class="marketplace-total-returned-units zero">(0)</span>
                </div>
                <div class="marketplace-col jp zero-sales">
                  <img src="./assets/JP.svg" alt="JP" class="marketplace-icon" width="28" height="28" />
                  <span class="marketplace-total-sales-count zero">0</span>
                  <span class="marketplace-total-earned-royalties zero">\xA50</span>
                  <span class="marketplace-total-returned-units zero">(0)</span>
                </div>
              </div>
            </div>
            <hr class="sales-section-divider" />
            <div class="lifetime-data-div">
              <div class="lifetime-data-item">
                <span class="lifetime-data-label">Revenue</span>
                <span class="lifetime-data-value revenue">$1,571,933.0</span>
              </div>
              <div class="lifetime-data-item">
                <span class="lifetime-data-label">Net Earnings</span>
                <span class="lifetime-data-value positive">$511,933.0</span>
              </div>
              <div class="lifetime-data-item">
                <span class="lifetime-data-label">Gross Earnings</span>
                <span class="lifetime-data-value gross-earnings">$571,933.0</span>
              </div>
              <div class="lifetime-data-item">
                <span class="lifetime-data-label">Taxes (US <span class="lifetime-tax-rate">30%</span>)</span>
                <span class="lifetime-data-value negative">-$61,933</span>
              </div>
              <div class="lifetime-data-item">
                <span class="lifetime-data-label">Refunds</span>
                <span class="lifetime-data-value negative">-$61,933</span>
              </div>
              <div class="lifetime-data-item">
                <span class="lifetime-data-label">Royalty/Sale</span>
                <span class="lifetime-data-value positive">$3.93</span>
              </div>
            </div>
          </div>

          <!-- Customer Reviews & Engagement Card -->
          <div class="customer-reviews-card-div">
            <div class="reviews-title-date-div">
              <div class="title-date-section">
                <img src="./assets/review-rating-ic.svg" alt="Reviews Rating Icon" class="reviews-card-icon" width="16" height="16" />
                <div class="title-date-text">
                  <span class="reviews-card-title">Customer Reviews & Engagement</span>
                </div>
              </div>
            </div>
            <div class="reviews-analytics-div">
              <div class="top-row" style="display:flex; align-items:center; gap:12px; width:100%;">
                <div class="reviews-count-div">
                  <span class="reviews-count">7,223</span>
                </div>
                <div class="comments-ratings-div" style="display:flex; align-items:flex-start; gap:8px; margin-left:auto;">
                  <div class="metric-col">
                    <span class="metric-label">Comments</span>
                    <span class="metric-value">327</span>
                  </div>
                  <div class="metric-divider"></div>
                  <div class="metric-col">
                    <span class="metric-label">Average Rating</span>
                    <span class="metric-value">4.2</span>
                  </div>
                </div>
              </div>
            </div>
            <hr class="reviews-section-divider" />
            <div class="marketplaces-div">
              <div class="marketplaces-reviews-row">
                <div class="marketplace-col us">
                  <img src="./assets/US.svg" alt="US" class="marketplace-icon" width="28" height="28" />
                  <span class="marketplace-total-reviews-count">4,153</span>
                </div>
                <div class="marketplace-col uk">
                  <img src="./assets/UK.svg" alt="UK" class="marketplace-icon" width="28" height="28" />
                  <span class="marketplace-total-reviews-count">223</span>
                </div>
                <div class="marketplace-col de">
                  <img src="./assets/DE.svg" alt="DE" class="marketplace-icon" width="28" height="28" />
                  <span class="marketplace-total-reviews-count">223</span>
                </div>
                <div class="marketplace-col fr">
                  <img src="./assets/FR.svg" alt="FR" class="marketplace-icon" width="28" height="28" />
                  <span class="marketplace-total-reviews-count">132</span>
                </div>
                <div class="marketplace-col it zero-sales">
                  <img src="./assets/IT.svg" alt="IT" class="marketplace-icon" width="28" height="28" />
                  <span class="marketplace-total-reviews-count zero">0</span>
                </div>
                <div class="marketplace-col es">
                  <img src="./assets/ES.svg" alt="ES" class="marketplace-icon" width="28" height="28" />
                  <span class="marketplace-total-reviews-count">32</span>
                </div>
                <div class="marketplace-col jp">
                  <img src="./assets/JP.svg" alt="JP" class="marketplace-icon" width="28" height="28" />
                  <span class="marketplace-total-reviews-count">232</span>
                </div>
              </div>
            </div>
            <hr class="reviews-section-divider" />
            <div class="reviews-list"></div>
          </div>
        </div>
      </div>

    </div>
  </div>
</div>
`;function formatNumberWithCommas(num){return num.toLocaleString()}function formatReturnedUnits(returnedValue){return returnedValue===0?"(0)":returnedValue>0?`(-${formatNumberWithCommas(returnedValue)})`:`(-${formatNumberWithCommas(Math.abs(returnedValue))})`}function formatReturnedMetric(returnedValue){return returnedValue===0?"0":`(-${formatNumberWithCommas(Math.abs(returnedValue))})`}let customTooltipMouseX=0,customTooltipMouseY=0;const activeCustomTooltips=new Map;function updateCustomTooltipMousePosition(e){customTooltipMouseX=e.clientX,customTooltipMouseY=e.clientY}function isElementUnderCustomTooltipMouse(element){const rect=element.getBoundingClientRect(),tolerance=2;return customTooltipMouseX>=rect.left-tolerance&&customTooltipMouseX<=rect.right+tolerance&&customTooltipMouseY>=rect.top-tolerance&&customTooltipMouseY<=rect.bottom+tolerance}let customTooltipScrollTimeout;function handleCustomTooltipScroll(){customTooltipScrollTimeout&&clearTimeout(customTooltipScrollTimeout),checkAndHideCustomTooltips(),customTooltipScrollTimeout=setTimeout(()=>{checkAndHideCustomTooltips()},16)}function checkAndHideCustomTooltips(){activeCustomTooltips.forEach((tooltipData,element)=>{tooltipData.isVisible&&(isElementUnderCustomTooltipMouse(element)?tooltipData.showFunction&&tooltipData.showFunction():tooltipData.hideFunction&&tooltipData.hideFunction())})}function initCustomTooltipScrollDetection(){window.EventCleanupManager.addEventListener(document,"mousemove",updateCustomTooltipMousePosition,{passive:!0});function addCustomTooltipScrollListeners(){window.EventCleanupManager.addEventListener(window,"scroll",handleCustomTooltipScroll,{passive:!0});const mainContent=document.querySelector(".main-content");mainContent&&window.EventCleanupManager.addEventListener(mainContent,"scroll",handleCustomTooltipScroll,{passive:!0}),document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div").forEach(card=>{window.EventCleanupManager.addEventListener(card,"scroll",handleCustomTooltipScroll,{passive:!0})}),document.querySelectorAll('[style*="overflow-y: auto"], [style*="overflow: auto"]').forEach(container=>{window.EventCleanupManager.addEventListener(container,"scroll",handleCustomTooltipScroll,{passive:!0})})}addCustomTooltipScrollListeners();const observer=window.EventCleanupManager.addMutationObserver(document.body,function(mutations){mutations.forEach(function(mutation){mutation.type==="childList"&&mutation.addedNodes.length>0&&setTimeout(addCustomTooltipScrollListeners,100)})},{childList:!0,subtree:!0})}function calculateMetricRemainingValues(){const accountStatus=document.querySelector(".account-status");if(!accountStatus)return;const metricSubtexts=accountStatus.querySelectorAll(".metric-subtext"),tooltipTexts=["Free Designs","Free Products","Remaining Quota"];metricSubtexts.forEach((subtextElement,index)=>{const text=subtextElement.textContent.trim(),remainingSpan=subtextElement.querySelector(".metric-remaining");if(!remainingSpan)return;const match=text.match(/^([\d,]+)\s+of\s+([\d,]+)/);if(match){const current=parseInt(match[1].replace(/,/g,""),10),formattedRemaining=(parseInt(match[2].replace(/,/g,""),10)-current).toLocaleString();remainingSpan.textContent=formattedRemaining,tooltipTexts[index]&&remainingSpan.setAttribute("data-tooltip",tooltipTexts[index])}})}function roundToHalf(rating){const safe=typeof rating=="number"&&isFinite(rating)?rating:0;return Math.max(0,Math.min(5,Math.round(safe*2)/2))}function renderStarRating(rating){const rounded=roundToHalf(rating),fullStars=Math.floor(rounded),hasHalf=rounded-fullStars>=.5,totalStars=5,starPath="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.62L12 2 9.19 8.62 2 9.24l5.46 4.73L5.82 21z";let html='<div class="rating-stars" aria-label="Rating: '+rounded.toFixed(1)+' out of 5">';for(let i=0;i<fullStars;i++)html+='<svg class="star" viewBox="0 0 24 24" aria-hidden="true"><path d="'+starPath+'" fill="#FDC300" stroke="#FDC300" stroke-width="2"/></svg>';hasHalf&&(html+='<svg class="star" viewBox="0 0 24 24" aria-hidden="true"><defs><clipPath id="halfFillDynamic"><rect x="0" y="0" width="12" height="24" /></clipPath></defs><path d="'+starPath+'" fill="#FDC300" clip-path="url(#halfFillDynamic)" /><path d="'+starPath+'" fill="none" stroke="#FDC300" stroke-width="2" stroke-linejoin="round" stroke-linecap="round"/></svg>');const remaining=totalStars-fullStars-(hasHalf?1:0);for(let i=0;i<remaining;i++)html+='<svg class="star" viewBox="0 0 24 24" aria-hidden="true"><path d="'+starPath+'" fill="none" stroke="#FDC300" stroke-width="2" stroke-linejoin="round" stroke-linecap="round"/></svg>';return html+="</div>",html+='<span class="rating-text">('+rounded.toFixed(1)+" out of 5)</span>",html}function renderCustomerReviews(reviews){const container=document.querySelector(".customer-reviews-card-div .reviews-list");if(!container)return;const parsed=(Array.isArray(reviews)?reviews:[]).map(r=>({...r,_ts:Date.parse(r.date||"")||0}));parsed.sort((a,b)=>b._ts-a._ts);const fragment=document.createDocumentFragment();parsed.forEach((r,idx)=>{const wrapper=document.createElement("div");if(wrapper.className="listing-review-div",wrapper.innerHTML='<div class="product-div"></div><div class="listing-div"><div class="title-marketplace-div"><div class="title-and-marketplace">'+(r.flagSrc?'<img src="'+r.flagSrc+'" class="marketplace-flag" width="20" height="20" />':"")+'<span class="listing-title">'+(r.title||"")+'</span></div><div class="view-btn"><img src="./assets/view-insights-ic.svg" alt="View" width="8" height="8" /><span>View</span></div></div><div class="listing-comment">'+(r.comment||"")+'</div><div class="rating-row">'+renderStarRating(Number(r.rating))+'</div><div class="review-date">'+(r.date||"")+"</div></div>",fragment.appendChild(wrapper),idx<parsed.length-1){const divider=document.createElement("hr");divider.className="listing-section-divider",fragment.appendChild(divider)}}),container.innerHTML="",container.appendChild(fragment)}function initDashboard(){initCustomTooltipScrollDetection(),testAdSalesExtraction();const mainContent=document.querySelector(".main-content");if(mainContent){mainContent.innerHTML=dashboardHTML,setTimeout(()=>{initializeListingIconTooltips()},0);try{renderCustomerReviews([{marketplace:"US",flagSrc:"./assets/US.svg",title:"Tree of Life vest top",comment:"Lovely top great fit and really comfy soft cotton :)",rating:3.5,date:"Jul 21, 2025"},{marketplace:"UK",flagSrc:"./assets/UK.svg",title:"Funny dad joke tee",comment:"Quality print and fabric, sizing was as expected.",rating:4,date:"Aug 12, 2025"},{marketplace:"DE",flagSrc:"./assets/DE.svg",title:"Retro gaming tee",comment:"Arrived on time and looks awesome in person.",rating:2.5,date:"Jun 03, 2025"},{marketplace:"JP",flagSrc:"./assets/JP.svg",title:"Minimal typographic tee",comment:"Material feels premium and soft after wash.",rating:5,date:"May 14, 2025"}])}catch{}}else return;document.querySelectorAll(".gradient-text").forEach(element=>{const existingOverlay=element.querySelector(".gradient-overlay");existingOverlay&&element.removeChild(existingOverlay)});const progressBars=document.querySelectorAll(".progress-fill");if(progressBars.length>0){const defaultWidths=["89%","70%","25%"];progressBars.forEach((bar,index)=>{let width=bar.style.width||defaultWidths[index]||"0%";(!width||width==="0%")&&(width=defaultWidths[index]||"50%"),bar.style.width="0%",setTimeout(()=>{bar.style.width=width},200+index*100)})}document.querySelectorAll(".metric-item").forEach(item=>{window.EventCleanupManager.addEventListener(item,"mouseenter",()=>{const fill=item.querySelector(".progress-fill");fill&&(fill.style.opacity="0.8")}),window.EventCleanupManager.addEventListener(item,"mouseleave",()=>{const fill=item.querySelector(".progress-fill");fill&&(fill.style.opacity="1")})}),document.querySelectorAll(".dashboard-card").forEach(card=>{window.EventCleanupManager.addEventListener(card,"click",()=>{card.style.transform="scale(0.98)",setTimeout(()=>{card.style.transform="scale(1)"},100)})});let lastUpdate=Date.now();function updateTimestamps(){const now=Date.now(),timeElements=document.querySelectorAll(".activity-time");now-lastUpdate<6e4||(timeElements.forEach(el=>{}),lastUpdate=now)}const updateInterval=window.EventCleanupManager.setInterval(updateTimestamps,6e4);updateTimestamps(),setupPrivacyMode();const dropdownMount=document.getElementById("dashboard-marketplace-dropdown-mount");dropdownMount&&(dropdownMount.innerHTML=getMarketplaceDropdownHTML(),setupMarketplaceDropdown()),calculateMetricRemainingValues(),initializeSearchFunctionality(),initMarketplaceToggle(),initializeSortingTabs(),initCustomTooltipsForMarketplaces(),setTimeout(()=>{initAdSpendMarketplaceTooltips()},200),setTimeout(()=>{initializeCustomTooltips()},100),initializeNewTabCounts(),initializeThemeChangeListener(),setTimeout(()=>{document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div").forEach((salesCard,index)=>{const analytics=calculateComprehensiveAnalytics(salesCard)})},2e3);let attempts=0;const maxAttempts=10;function tryInitializeAdSpend(){attempts++,document.querySelectorAll(".listing-ad-row").length>0?initializeAdSpendStatus():attempts<maxAttempts&&setTimeout(tryInitializeAdSpend,200)}setTimeout(tryInitializeAdSpend,100),updateSalesCardDates(),updateTodayVsPreviousYearsDate(),updateMonthlySalesDate(),updateYearlySalesDate(),initializeLazyLoadedCharts(),setTimeout(()=>{initializeMonthlySalesButtons()},900),setTimeout(()=>{initializeShowHideOptionsButtons()},1e3);let adSpendTimeframeIndex=0;function updateAdSpendSection(){const label=document.querySelector(".ad-spend-today-label");label&&(label.textContent=adSpendTimeframes[adSpendTimeframeIndex]);const spend=document.querySelector(".ad-spend-header-value"),orders=document.querySelectorAll(".ad-spend-header-value")[1];spend&&(spend.textContent=adSpendMockData[adSpendTimeframeIndex].spend),orders&&(orders.textContent=adSpendMockData[adSpendTimeframeIndex].orders);const marketplaceSelectors=[".ad-spend-marketplace-col-us",".ad-spend-marketplace-col-uk",".ad-spend-marketplace-col-de",".ad-spend-marketplace-col-fr",".ad-spend-marketplace-col-it",".ad-spend-marketplace-col-es",".ad-spend-marketplace-col-jp"];adSpendMockData[adSpendTimeframeIndex].marketplaces.forEach((mkt,i)=>{const col=document.querySelector(marketplaceSelectors[i]);if(!col)return;const val=col.querySelector(".ad-spend-value.ad-spend-currency");val&&(val.textContent=mkt.currency);const order=col.querySelector(".ad-spend-orders");order&&(order.textContent=`(${mkt.orders})`);const acos=col.querySelector(".ad-spend-acos-value");acos&&(acos.textContent=mkt.acos);const flag=col.querySelector(".ad-spend-flag");flag&&(flag.src=mkt.flag);const acosIcon=col.querySelector(".ad-spend-acos-pill");acosIcon&&mkt.acosIcon&&(acosIcon.src=mkt.acosIcon)}),window.initAdSpendMarketplaceTooltips&&window.initAdSpendMarketplaceTooltips()}setTimeout(()=>{const prevBtn=document.querySelector(".ad-spend-prev"),nextBtn=document.querySelector(".ad-spend-next"),adSpend=document.querySelector(".ad-spend");let isAdSpendLoading=!1,adSpendLoadingTimeout=null;function showAdSpendLoader(){if(adSpend&&window.SnapLoader){isAdSpendLoading=!0,disableAdSpendNavigation(),adSpendLoadingTimeout&&clearTimeout(adSpendLoadingTimeout),adSpendLoadingTimeout=setTimeout(()=>{isAdSpendLoading=!1,enableAdSpendNavigation(),window.SnapLoader&&window.SnapLoader.hideOverlay(adSpend)},1e4);const loaderSize=adSpend.parentElement&&adSpend.parentElement.classList.contains("account-ad-spend-wrapper")?"medium":"small",overlay=window.SnapLoader.showOverlay(adSpend,{text:"Updating ad spend data...",id:"ad-spend-period-loader",size:loaderSize});overlay&&(overlay.classList.add("snap-loader-compact"),adSpend.firstChild!==overlay&&adSpend.insertBefore(overlay,adSpend.firstChild))}}function hideAdSpendLoader(){adSpendLoadingTimeout&&(clearTimeout(adSpendLoadingTimeout),adSpendLoadingTimeout=null),adSpend&&window.SnapLoader?setTimeout(()=>{window.SnapLoader.hideOverlay(adSpend),isAdSpendLoading=!1,enableAdSpendNavigation()},300):(isAdSpendLoading=!1,enableAdSpendNavigation())}function disableAdSpendNavigation(){const buttons=[prevBtn,nextBtn].filter(btn=>btn);updateButtonStatesOptimized(buttons,!0),buttons.forEach(btn=>{btn.setAttribute("aria-disabled","true"),btn.setAttribute("title","Loading ad spend data...")})}function enableAdSpendNavigation(){const buttons=[prevBtn,nextBtn].filter(btn=>btn);updateButtonStatesOptimized(buttons,!1),buttons.forEach((btn,index)=>{btn.removeAttribute("aria-disabled"),btn.setAttribute("title",index===0?"Previous period":"Next period")})}prevBtn&&window.EventCleanupManager.addEventListener(prevBtn,"click",()=>{isAdSpendLoading||(showAdSpendLoader(),adSpendTimeframeIndex=(adSpendTimeframeIndex-1+adSpendTimeframes.length)%adSpendTimeframes.length,updateAdSpendSection(),hideAdSpendLoader())}),nextBtn&&window.EventCleanupManager.addEventListener(nextBtn,"click",()=>{isAdSpendLoading||(showAdSpendLoader(),adSpendTimeframeIndex=(adSpendTimeframeIndex+1)%adSpendTimeframes.length,updateAdSpendSection(),hideAdSpendLoader())}),updateAdSpendSection()},0),initializeDOMOptimizations(),window.testAdSpendStatus=initializeAdSpendStatus,window.forceAdSpendUpdate=()=>{const adRows=document.querySelectorAll(".listing-ad-row");initializeAdSpendStatus()},window.testProgressBars=()=>{document.querySelectorAll(".progress-fill").forEach((bar,index)=>{const targetWidth=bar.style.width||"0%";bar.style.width="0%",setTimeout(()=>{bar.style.width=targetWidth},100+index*100)})},window.dashboardInitializeCustomTooltips=initializeCustomTooltips,initializeDynamicProductImageBackgrounds(),setTimeout(async()=>{await applyFourSalesCardsMockData(),initializeMarketplaceOpacityMonitoring(),seedHideAllPercentages(),window.FourSalesCardsRealTimeManager&&window.FourSalesCardsRealTimeManager.start(3e4)},1e3),setTimeout(async()=>{await applyTopFourSalesCardsMockData(),enforceZeroPercentageVisibility()},1200),window.EventCleanupManager.addEventListener(window,"beforeunload",function(){stopRealTimeColorMonitoring()}),window.EventCleanupManager.addEventListener(window,"componentUnloaded",function(e){e.detail.component==="dashboard"&&stopRealTimeColorMonitoring()}),hideBadgesWithZeroValues(),hideAdRowsWithZeroValues(),setTimeout(()=>{restoreAllSalesAnalytics(),initDynamicSalesCardPadding(),enforceZeroPercentageVisibility()},500),initializeDateChangeManager(),initializeTimeTracker(),initializeZeroPercentageObserver(),startZeroPercentageEnforcementLoop()}function enforceZeroPercentageVisibility(scopeEl=document){try{scopeEl.querySelectorAll(".metric-percentage, .comparison-percentage, .snap-chart-insight-value.return-rate, .snap-chart-insight-value.conversion-days-percentage").forEach(node=>{const text=(node.textContent||"").trim(),numeric=parseFloat(text.replace("%","")),isPercentZero=!isNaN(numeric)&&numeric===0;let associatedIsZero=!1;const metricCol=node.closest(".metric-col");if(metricCol){const valueEl=metricCol.querySelector(".metric-value");if(valueEl){const numericText=(valueEl.textContent||"").trim().replace(/[^0-9.-]/g,""),valueNum=parseFloat(numericText);associatedIsZero=isNaN(valueNum)?!1:valueNum===0}}isPercentZero||associatedIsZero?(node.style.display="none",node.classList.add("zero")):node.classList.contains("zero")&&(node.style.display="",node.classList.remove("zero"))})}catch{}}typeof window<"u"&&(window.enforceZeroPercentageVisibility=enforceZeroPercentageVisibility);function refreshCardNoDataUI(salesCard,hasData){try{const dateEl=salesCard.querySelector(".sales-card-date"),insightsBtn=salesCard.querySelector(".view-insights-btn");hasData?(dateEl&&dateEl.dataset.originalText&&(dateEl.textContent=dateEl.dataset.originalText),insightsBtn&&(insightsBtn.classList.remove("disabled"),insightsBtn.removeAttribute("aria-disabled"),insightsBtn.style.pointerEvents="",insightsBtn.style.opacity="")):(dateEl&&(dateEl.dataset.originalText||(dateEl.dataset.originalText=(dateEl.textContent||"").trim()),dateEl.textContent="Not defined, yet."),insightsBtn&&(insightsBtn.classList.add("disabled"),insightsBtn.setAttribute("aria-disabled","true"),insightsBtn.style.pointerEvents="none",insightsBtn.style.opacity="0.5"))}catch{}}function seedHideAllPercentages(){document.querySelectorAll(".four-sales-cards-section .metric-percentage, .top-four-sales-cards-section .metric-percentage").forEach(el=>{el.classList.add("zero"),el.style.display="none",el.textContent&&el.textContent.trim()===""&&(el.textContent="0.0%")})}function initializeZeroPercentageObserver(){try{const root=document.querySelector(".sales-cards-container")||document,observer=new MutationObserver(mutations=>{let needsEnforce=!1;mutations.forEach(m=>{if(m.type==="childList"||m.type==="characterData"){const t=m.target;t&&(t.nodeType===3||t.classList&&(t.classList.contains("metric-value")||t.classList.contains("metric-percentage")))&&(needsEnforce=!0)}}),needsEnforce&&enforceZeroPercentageVisibility()});observer.observe(root,{subtree:!0,childList:!0,characterData:!0}),window.__zeroPctObserver=observer}catch{}}function pauseZeroPercentageObserver(){try{window.__zeroPctObserver&&(window.__zeroPctObserver.disconnect(),window.__zeroPctObserver=null)}catch{}}function resumeZeroPercentageObserver(){try{initializeZeroPercentageObserver(),enforceZeroPercentageVisibility(),startZeroPercentageEnforcementLoop()}catch{}}function startZeroPercentageEnforcementLoop(){let runsRemaining=8;const tick=()=>{enforceZeroPercentageVisibility(),runsRemaining--,runsRemaining>0&&setTimeout(tick,200)};setTimeout(tick,0)}function initializeDateChangeManager(){window.DateChangeManager&&(window.DateChangeManager.registerCallback(async(previousDate,currentDate)=>{try{updateSalesCardDates(),updateTodayVsPreviousYearsDate(),updateMonthlySalesDate(),updateYearlySalesDate()}catch{}},1),window.DateChangeManager.registerCallback(async(previousDate,currentDate)=>{try{window.refreshFourSalesCardsData&&await window.refreshFourSalesCardsData()}catch{}},2),window.DateChangeManager.registerCallback(async(previousDate,currentDate)=>{try{const chartContainer=document.getElementById("today-vs-previous-years-chart-container");if(chartContainer&&chartContainer.snapChart){const newData=await window.generateTodayVsPreviousYearsDataCached();chartContainer.snapChart.updateData(newData)}}catch{}},3),window.DateChangeManager.registerCallback(async(previousDate,currentDate)=>{try{window.clearDateBasedCaches&&window.clearDateBasedCaches()}catch{}},4),window.DateChangeManager.registerCallback(async(previousDate,currentDate)=>{try{document.querySelectorAll("[data-timestamp]").forEach(element=>{(element.textContent.includes("today")||element.textContent.includes("yesterday"))&&element.dispatchEvent(new Event("refresh-timestamp"))})}catch{}},5),window.DateChangeManager.startMonitoring(),setTimeout(()=>{window.checkDateChangeManagerHealth&&window.checkDateChangeManagerHealth()},3e3),window.EventCleanupManager.addEventListener(window,"componentUnloaded",function(e){e.detail.component==="dashboard"&&window.DateChangeManager.stopMonitoring()}))}function initializeTimeTracker(){const localTimeDisplay=document.getElementById("local-time-display"),pacificTimeDisplay=document.getElementById("pacific-time-display");if(!localTimeDisplay||!pacificTimeDisplay)return;function updateTimeDisplays(){try{const localTimeString=new Date().toLocaleTimeString("en-US",{hour12:!0,hour:"2-digit",minute:"2-digit",second:"2-digit"});let pacificTimeString="00:00:00";window.SnapTimezone&&typeof window.SnapTimezone.getPacificTime=="function"?pacificTimeString=window.SnapTimezone.getPacificTime().toLocaleTimeString("en-US",{hour12:!0,hour:"2-digit",minute:"2-digit",second:"2-digit"}):pacificTimeString=new Date().toLocaleTimeString("en-US",{timeZone:"America/Los_Angeles",hour12:!0,hour:"2-digit",minute:"2-digit",second:"2-digit"}),localTimeDisplay.textContent=localTimeString,pacificTimeDisplay.textContent=pacificTimeString}catch{localTimeDisplay.textContent="12:00:00 AM",pacificTimeDisplay.textContent="12:00:00 AM"}}updateTimeDisplays();let timeUpdateCallbackId=null;if(window.UnifiedTimerManager)timeUpdateCallbackId=window.UnifiedTimerManager.registerCallback(updateTimeDisplays,1e3,"timeTracker"),window.UnifiedTimerManager.isActive||window.UnifiedTimerManager.start();else{const timeUpdateInterval=window.EventCleanupManager?window.EventCleanupManager.setInterval(updateTimeDisplays,1e3):setInterval(updateTimeDisplays,1e3);window.EventCleanupManager.addEventListener(window,"componentUnloaded",function(e){e.detail.component==="dashboard"&&(window.EventCleanupManager?window.EventCleanupManager.clearInterval(timeUpdateInterval):clearInterval(timeUpdateInterval))})}timeUpdateCallbackId!==null&&window.EventCleanupManager.addEventListener(window,"componentUnloaded",function(e){e.detail.component==="dashboard"&&window.UnifiedTimerManager.unregisterCallback(timeUpdateCallbackId)})}function hideBadgesWithZeroValues(){const canceledBadges=document.querySelectorAll(".canceled-units-badge");let hiddenCanceledCount=0;canceledBadges.forEach(badge=>{const valueSpan=badge.querySelector("span:last-child");valueSpan&&valueSpan.textContent.trim()==="0"&&(badge.style.display="none",hiddenCanceledCount++)});const returnedBadges=document.querySelectorAll(".returned-units-badge");let hiddenReturnedCount=0;returnedBadges.forEach(badge=>{const valueSpan=badge.querySelector("span:last-child");if(valueSpan){const value=valueSpan.textContent.trim();(value==="0"||value==="-0"||value==="+0")&&(badge.style.display="none",hiddenReturnedCount++)}})}function hideAdRowsWithZeroValues(){const adRows=document.querySelectorAll(".listing-ad-row");let hiddenCount=0;adRows.forEach(adRow=>{let shouldHide=!1;adRow.classList.contains("ad-no-sales")&&(shouldHide=!0);const adLabel=adRow.querySelector(".listing-ad-label");if(adLabel&&!shouldHide){const adText=adLabel.textContent.trim();[/\$0(\.|,)?0?\s*\(0\)/,/€0(\.|,)?0?\s*\(0\)/,/£0(\.|,)?0?\s*\(0\)/,/¥0(\.|,)?0?\s*\(0\)/,/\(0\)/,/^0(\.|,)?0?$/,/no sales/i,/no ad spend/i].some(pattern=>pattern.test(adText))&&(shouldHide=!0)}shouldHide?(adRow.style.display="none",hiddenCount++):adRow.style.display=""}),adjustListingLayoutForHiddenAdRows()}function adjustListingLayoutForHiddenAdRows(){const allListings=document.querySelectorAll(".listing-analytics-div");let adjustedCount=0;allListings.forEach(listing=>{const adRow=listing.querySelector(".listing-ad-row"),rightDiv=listing.querySelector(".listing-right-div");if(!adRow||!rightDiv)return;adRow.style.display==="none"||window.getComputedStyle(adRow).display==="none"?(adjustToNonAdLayout(rightDiv),adjustedCount++):adjustToNormalLayout(rightDiv)})}function adjustToNonAdLayout(rightDiv){const productTypeRow=rightDiv.querySelector(".listing-product-type-row"),priceOnlyDiv=rightDiv.querySelector(".listing-product-price-only"),editAnalyseRow=rightDiv.querySelector(".listing-edit-analyse-row");if(!productTypeRow)return;if(!priceOnlyDiv){const priceElement=productTypeRow.querySelector(".listing-product-price");if(priceElement){const newPriceOnlyDiv=document.createElement("div");newPriceOnlyDiv.className="listing-product-price-only";const clonedPrice=priceElement.cloneNode(!0);newPriceOnlyDiv.appendChild(clonedPrice),priceElement.remove(),rightDiv.insertBefore(newPriceOnlyDiv,rightDiv.firstChild)}}const children=Array.from(rightDiv.children),priceDiv=children.find(child=>child.classList.contains("listing-product-price-only")),typeRow=children.find(child=>child.classList.contains("listing-product-type-row")),editRow=children.find(child=>child.classList.contains("listing-edit-analyse-row"));priceDiv&&typeRow&&editRow&&(rightDiv.innerHTML="",rightDiv.appendChild(priceDiv),rightDiv.appendChild(typeRow),rightDiv.appendChild(editRow))}function adjustToNormalLayout(rightDiv){const productTypeRow=rightDiv.querySelector(".listing-product-type-row"),priceOnlyDiv=rightDiv.querySelector(".listing-product-price-only"),adRow=rightDiv.querySelector(".listing-ad-row"),editAnalyseRow=rightDiv.querySelector(".listing-edit-analyse-row");if(priceOnlyDiv&&productTypeRow){const priceInTypeRow=productTypeRow.querySelector(".listing-product-price"),priceInOnlyDiv=priceOnlyDiv.querySelector(".listing-product-price");if(!priceInTypeRow&&priceInOnlyDiv){const clonedPrice=priceInOnlyDiv.cloneNode(!0);productTypeRow.appendChild(clonedPrice),priceOnlyDiv.remove()}}const children=Array.from(rightDiv.children),typeRow=children.find(child=>child.classList.contains("listing-product-type-row")),adRowElement=children.find(child=>child.classList.contains("listing-ad-row")),editRow=children.find(child=>child.classList.contains("listing-edit-analyse-row"));typeRow&&editRow&&(rightDiv.innerHTML="",rightDiv.appendChild(typeRow),adRowElement&&rightDiv.appendChild(adRowElement),rightDiv.appendChild(editRow))}function initializeSearchFunctionality(){document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div").forEach((salesCard,cardIndex)=>{initializeCardSearch(salesCard,cardIndex)})}function initializeCardSearch(salesCard,cardIndex){const searchInput=salesCard.querySelector(".search-input"),closeSearchIcon=salesCard.querySelector(".close-search-icon"),salesFilterDiv=salesCard.querySelector(".sales-filter-div"),listingAnalyticsDivs=salesCard.querySelectorAll(".listing-analytics-div"),listingDividers=salesCard.querySelectorAll(".listing-section-divider"),searchNoResults=salesCard.querySelector(".search-no-results"),searchTermDisplay=salesCard.querySelector(".search-term-display");if(!searchInput||!closeSearchIcon||!salesFilterDiv||listingAnalyticsDivs.length===0||!searchNoResults)return;const mockListingsData={0:[{title:"Vintage Music Band Rock Concert Tour Design",asin:"B0ST12345A",productType:"S. T-Shirt",price:"$19.99"},{title:"Funny Cat Design Premium Quality Cotton",asin:"B0PT12345B",productType:"P. T-Shirt",price:"\xA318.99"},{title:"Cool Dog Graphic V-Neck Premium Comfort",asin:"B0VN12345C",productType:"V-Neck",price:"\u20AC21.99"},{title:"Summer Beach Vibes Tank Top Design",asin:"B0TK12345D",productType:"Tank Top",price:"\u20AC16.99"},{title:"Motivational Quote Sweatshirt Design",asin:"B0SW12345E",productType:"Sweatshirt",price:"\u20AC24.99"},{title:"Japanese Cultural Art Hoodie Design",asin:"B0PC12345F",productType:"Hoodie",price:"\xA52,999"}],1:[{title:"Vintage Music Band Rock Concert Tour Coffee Mug",asin:"B0MN78901P",productType:"Mug",price:"$14.99"},{title:"Motivational Quote Inspirational Poster Design",asin:"B0QR45678S",productType:"Poster",price:"$12.99"},{title:"Funny Animal Cartoon Character Sticker Pack",asin:"B0TU90123V",productType:"Sticker",price:"$8.99"},{title:"German Oktoberfest Design",asin:"B0DE78901G",productType:"T-Shirt",price:"\u20AC19.99"},{title:"Italian Cuisine Art Design",asin:"B0IT56789B",productType:"Apron",price:"\u20AC22.99"},{title:"Spanish Flamenco Dance Design",asin:"B0ES12345V",productType:"Dress",price:"\u20AC35.99"}]},mockListings=mockListingsData[cardIndex]||mockListingsData[0];let isSearchActive=!1,currentSearchTerm="";function updateCloseIconVisibility(){searchInput.value.trim().length>0?(closeSearchIcon.style.opacity="1",closeSearchIcon.style.pointerEvents="auto"):(closeSearchIcon.style.opacity="0",closeSearchIcon.style.pointerEvents="none")}function performSearch(searchTerm){const trimmedTerm=searchTerm.trim().toLowerCase();if(trimmedTerm===""){showNormalState();return}currentSearchTerm=searchTerm.trim();const activeMarketplace=getActiveMarketplaceForCard2(cardIndex),results=getVisibleListingsForMarketplace(activeMarketplace).filter(listing=>{const titleMatch=listing.title.toLowerCase().includes(trimmedTerm),asinMatch=listing.asin.toLowerCase().includes(trimmedTerm);return titleMatch||asinMatch});results.length===0?showNoResultsState():showSearchResults(results)}function getActiveMarketplaceForCard2(cardIndex2){if(globalMarketplaceFocus!=="all")return globalMarketplaceFocus;const marketplaceCols=salesCard.querySelectorAll(".marketplace-col");for(let col of marketplaceCols)if(col.classList.contains("active")&&!col.classList.contains("inactive")){if(col.classList.contains("all-marketplaces"))return"all";if(col.classList.contains("us"))return"us";if(col.classList.contains("uk"))return"uk";if(col.classList.contains("de"))return"de";if(col.classList.contains("fr"))return"fr";if(col.classList.contains("it"))return"it";if(col.classList.contains("es"))return"es";if(col.classList.contains("jp"))return"jp"}if(salesCard.querySelector(".marketplaces-div.single-marketplace-active")){for(let col of marketplaceCols)if(!col.classList.contains("inactive")&&!col.classList.contains("all-marketplaces")){if(col.classList.contains("us"))return"us";if(col.classList.contains("uk"))return"uk";if(col.classList.contains("de"))return"de";if(col.classList.contains("fr"))return"fr";if(col.classList.contains("it"))return"it";if(col.classList.contains("es"))return"es";if(col.classList.contains("jp"))return"jp"}}return"all"}function getVisibleListingsForMarketplace(marketplace){if(marketplace==="all")return mockListings;const visibleListingDivs=Array.from(salesCard.querySelectorAll(".listing-analytics-div")).filter(div=>window.getComputedStyle(div).display!=="none"),visibleListings=[];return visibleListingDivs.forEach(div=>{const titleElement=div.querySelector(".listing-title"),asinElement=div.querySelector(".Asin-badge span:last-child");if(titleElement||asinElement){const titleText=titleElement?titleElement.textContent.trim():"",asinText=asinElement?asinElement.textContent.trim():"",matchingListing=mockListings.find(listing=>asinText&&listing.asin===asinText||titleText&&listing.title.toLowerCase().includes(titleText.toLowerCase().substring(0,20)));matchingListing?visibleListings.push(matchingListing):visibleListings.push({title:titleText,asin:asinText,productType:"Product",price:"$0.00"})}}),visibleListings}function checkIfListingMatchesMarketplace2(flagSrc,marketplace){const marketplaceFlags={us:"US.svg",uk:"UK.svg",de:"DE.svg",fr:"FR.svg",it:"IT.svg",es:"ES.svg",jp:"JP.svg"};return flagSrc.includes(marketplaceFlags[marketplace])}function showNoResultsState(){isSearchActive=!0,searchTermDisplay.textContent=currentSearchTerm,listingAnalyticsDivs.forEach(div=>div.style.display="none"),listingDividers.forEach(divider=>divider.style.display="none"),salesFilterDiv.style.display="none";const noSalesState=salesCard.querySelector(".no-sales-state");noSalesState&&(noSalesState.style.display="none"),searchNoResults.style.display="block"}function showSearchResults(results){isSearchActive=!0,searchNoResults.style.display="none";const noSalesState=salesCard.querySelector(".no-sales-state");noSalesState&&(noSalesState.style.display="none"),salesFilterDiv.style.display="flex";const resultASINs=results.map(r=>r.asin.toLowerCase()),resultTitles=results.map(r=>r.title.toLowerCase()),activeMarketplace=getActiveMarketplaceForCard2(cardIndex);listingAnalyticsDivs.forEach((div,index)=>{const listingTitle=div.querySelector(".listing-title"),listingASINElement=div.querySelector(".Asin-badge span:last-child"),marketplaceFlag=div.querySelector(".listing-marketplace-flag");let shouldShow=!1;const searchTerm=currentSearchTerm.toLowerCase();let isVisibleForMarketplace=!0;if(activeMarketplace!=="all"&&marketplaceFlag){const flagSrc=marketplaceFlag.src;isVisibleForMarketplace=checkIfListingMatchesMarketplace2(flagSrc,activeMarketplace)}isVisibleForMarketplace&&(listingTitle&&(shouldShow=listingTitle.textContent.toLowerCase().includes(searchTerm)),!shouldShow&&listingASINElement&&(shouldShow=listingASINElement.textContent.toLowerCase().includes(searchTerm)),shouldShow||(shouldShow=results.some(result=>{const resultTitle=result.title.toLowerCase(),resultASIN=result.asin.toLowerCase();return listingTitle&&resultTitle.includes(listingTitle.textContent.toLowerCase().substring(0,30))||listingASINElement&&resultASIN===listingASINElement.textContent.toLowerCase()}))),shouldShow?(div.style.display="flex",index<listingDividers.length&&(listingDividers[index].style.display="block")):(div.style.display="none",index<listingDividers.length&&(listingDividers[index].style.display="none"))});const visibleListings=Array.from(listingAnalyticsDivs).filter(div=>div.style.display!=="none");if(visibleListings.length>0){const lastVisibleIndex=Array.from(listingAnalyticsDivs).indexOf(visibleListings[visibleListings.length-1]);lastVisibleIndex<listingDividers.length&&(listingDividers[lastVisibleIndex].style.display="none")}updateNewTabCount(salesCard)}function showNormalState(){isSearchActive=!1,currentSearchTerm="",searchNoResults.style.display="none";const selectedMarketplace=getActiveMarketplaceForCard2(cardIndex);salesFilterDiv.style.display="flex",listingAnalyticsDivs.forEach(listingDiv=>{const marketplaceFlag=listingDiv.querySelector(".listing-marketplace-flag");if(selectedMarketplace==="all")listingDiv.style.display="flex";else if(marketplaceFlag){const flagSrc=marketplaceFlag.src,shouldShow=checkIfListingMatchesMarketplace2(flagSrc,selectedMarketplace);listingDiv.style.display=shouldShow?"flex":"none"}});const visibleListings=Array.from(listingAnalyticsDivs).filter(div=>window.getComputedStyle(div).display!=="none");if(listingDividers.forEach(divider=>divider.style.display="none"),visibleListings.length>=2)for(let i=0;i<visibleListings.length-1;i++){const currentListingIndex=Array.from(listingAnalyticsDivs).indexOf(visibleListings[i]);currentListingIndex<listingDividers.length&&(listingDividers[currentListingIndex].style.display="block")}updateNewTabCount(salesCard)}function clearSearch(){searchInput.value="",updateCloseIconVisibility(),showNormalState(),searchInput.focus()}window.EventCleanupManager.addEventListener(searchInput,"input",e=>{updateCloseIconVisibility(),performSearch(e.target.value)}),window.EventCleanupManager.addEventListener(searchInput,"keydown",e=>{e.key==="Escape"&&clearSearch()}),window.EventCleanupManager.addEventListener(closeSearchIcon,"click",clearSearch),updateCloseIconVisibility()}let globalMarketplaceFocus="all",originalChartData=null,originalMonthlySalesData=null,originalYearlySalesData=null;window.globalMarketplaceFocus="all";const marketplaceOptions=[{icon:"./assets/all-marketplaces-ic.svg",label:"All Marketplaces",value:"all"},{icon:"./assets/US.svg",label:"United States",value:"us"},{icon:"./assets/UK.svg",label:"United Kingdom",value:"uk"},{icon:"./assets/DE.svg",label:"Germany",value:"de"},{icon:"./assets/FR.svg",label:"France",value:"fr"},{icon:"./assets/IT.svg",label:"Italy",value:"it"},{icon:"./assets/ES.svg",label:"Spain",value:"es"},{icon:"./assets/JP.svg",label:"Japan",value:"jp"}];function getMarketplaceDropdownHTML(selected=marketplaceOptions[0]){return`
    <div class="database-marketplace-dropdown snap-dropdown" id="marketplaceDropdown" data-tooltip="Marketplace Focus">
      <div class="dropdown-header" tabindex="0">
        <div class="dropdown-header-content">
          <img src="${selected.icon}" alt="${selected.label} Icon" width="16" height="16" class="dropdown-selected-icon" style="margin-right:0;" />
          <span class="dropdown-selected-label" style="font-size:12px;vertical-align:middle;position:relative;top:1px;">${selected.label}</span>
        </div>
        <img src="./assets/dropdown-ic.svg" alt="Dropdown" class="dropdown-arrow" />
      </div>
      <div class="dropdown-menu hidden" style="height:auto;max-height:none;width:100%;min-width:180px;">
        <div class="dropdown-list" style="max-height:none;overflow:visible;">
          ${marketplaceOptions.map(opt=>`
            <div class="dropdown-item${opt.value===selected.value?" selected":""}" data-value="${opt.value}" style="display:flex;align-items:center;font-size:12px;padding:12px;cursor:pointer;">
              <img src="${opt.icon}" alt="${opt.label} Icon" width="16" height="16" style="margin-right:10px;" />
              <span style="font-size:12px;vertical-align:middle;position:relative;top:1px;${opt.value===selected.value?"font-weight:bold;color:#470CED;":""}">${opt.label}</span>
            </div>
          `).join("")}
        </div>
      </div>
    </div>
  `}function setupMarketplaceDropdown(){const dropdown=document.getElementById("marketplaceDropdown");if(!dropdown)return;const header=dropdown.querySelector(".dropdown-header"),menu=dropdown.querySelector(".dropdown-menu"),items=dropdown.querySelectorAll(".dropdown-item"),selectedIcon=dropdown.querySelector(".dropdown-selected-icon"),selectedLabel=dropdown.querySelector(".dropdown-selected-label");let isOpen=!1;function hideActiveTooltip(){const event=new MouseEvent("mouseleave",{bubbles:!0});dropdown.dispatchEvent(event)}function openDropdown(){dropdown.classList.add("focused"),menu.classList.remove("hidden"),header.setAttribute("aria-expanded","true"),isOpen=!0,hideActiveTooltip()}function closeDropdown(){dropdown.classList.remove("focused"),menu.classList.add("hidden"),header.setAttribute("aria-expanded","false"),isOpen=!1}window.GlobalDropdownManager&&GlobalDropdownManager.register("marketplaceDropdown",openDropdown,closeDropdown,"marketplace"),window.EventCleanupManager.addEventListener(header,"click",e=>{e.stopPropagation(),isOpen?window.GlobalDropdownManager?GlobalDropdownManager.hide("marketplaceDropdown"):closeDropdown():(window.GlobalDropdownManager?GlobalDropdownManager.show("marketplaceDropdown"):openDropdown(),header.focus())}),window.EventCleanupManager.addEventListener(header,"focus",e=>{}),window.EventCleanupManager.addEventListener(header,"keydown",e=>{e.key==="Escape"&&closeDropdown()}),items.forEach(item=>{window.EventCleanupManager.addEventListener(item,"click",e=>{const value=item.getAttribute("data-value"),option=marketplaceOptions.find(opt=>opt.value===value);if(option){selectedIcon.src=option.icon,selectedIcon.alt=option.label+" Icon",selectedLabel.textContent=option.label,items.forEach(i=>{i.classList.remove("selected");const span2=i.querySelector("span");span2&&(span2.style.fontWeight="",span2.style.color="")}),item.classList.add("selected");const span=item.querySelector("span");span&&(span.style.fontWeight="bold",span.style.color="#470CED"),window.GlobalDropdownManager?GlobalDropdownManager.hide("marketplaceDropdown"):closeDropdown(),applyMarketplaceFocus(value)}})});function handleClickOutside(e){dropdown.contains(e.target)||(window.GlobalDropdownManager?GlobalDropdownManager.hide("marketplaceDropdown"):closeDropdown())}window.EventCleanupManager.addEventListener(document,"click",handleClickOutside)}function clearAllSearchStates(){document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div").forEach((salesCard,cardIndex)=>{const searchInput=salesCard.querySelector(".search-input"),closeSearchIcon=salesCard.querySelector(".close-search-icon"),searchNoResults=salesCard.querySelector(".search-no-results"),salesFilterDiv=salesCard.querySelector(".sales-filter-div"),listingAnalyticsDivs=salesCard.querySelectorAll(".listing-analytics-div"),listingDividers=salesCard.querySelectorAll(".listing-section-divider");if(searchInput&&searchNoResults){searchInput.value="",closeSearchIcon&&(closeSearchIcon.style.opacity="0",closeSearchIcon.style.pointerEvents="none"),searchNoResults.style.display="none",salesFilterDiv&&(salesFilterDiv.style.display="flex"),listingAnalyticsDivs.forEach(div=>{div.style.display="flex"}),listingDividers.forEach(divider=>{divider.style.display="block"});const noSalesState=salesCard.querySelector(".no-sales-state");noSalesState&&(noSalesState.style.display="none")}})}function applyMarketplaceFocus(selectedMarketplace){clearAllSearchStates(),selectedMarketplace!==globalMarketplaceFocus&&resetCompareMode();const salesCards=document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div");let showSalesCardLoadersTimeout=null;window.SnapLoader&&(showSalesCardLoadersTimeout=setTimeout(()=>{salesCards.forEach(card=>{card.setAttribute("aria-busy","true"),window.SnapLoader.showSalesCardLoading(card,"Updating...")})},150)),window.marketplaceUpdateSeq=(window.marketplaceUpdateSeq||0)+1;const updateToken=window.marketplaceUpdateSeq;setTimeout(async()=>{try{pauseZeroPercentageObserver(),selectedMarketplace==="all"?await showAllMarketplaceElements():applyFocusedMarketplaceFiltering(selectedMarketplace),updateChartForMarketplace(selectedMarketplace),globalMarketplaceFocus=selectedMarketplace,window.globalMarketplaceFocus=selectedMarketplace,salesCards.forEach(salesCard=>{setTimeout(()=>{updateNewTabCount(salesCard),updateAllPercentageCalculations(salesCard,selectedMarketplace),enforceZeroPercentageVisibility(salesCard)},20)}),updateTopFourCardsForMarketplace(selectedMarketplace,updateToken),enforceZeroPercentageVisibility()}finally{resumeZeroPercentageObserver(),showSalesCardLoadersTimeout&&clearTimeout(showSalesCardLoadersTimeout),window.SnapLoader&&salesCards.forEach(card=>{window.SnapLoader.hideSalesCardLoading(card),card.removeAttribute("aria-busy")})}},50)}async function showAllMarketplaceElements(){const horizontalWrapper=document.querySelector(".account-ad-spend-wrapper"),accountStatus=document.querySelector(".account-status"),adSpend=document.querySelector(".ad-spend"),listingsStatusOverview=document.querySelector(".listings-status-overview");if(horizontalWrapper&&accountStatus&&adSpend&&listingsStatusOverview){const parentContainer=horizontalWrapper.parentNode;accountStatus.style.cssText=`
      width: 100%;
      min-height: 92px;
      background: var(--bg-primary);
      border-radius: 14px;
      padding: 24px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
    `,adSpend.style.cssText=`
      width: 100%;
      background: var(--bg-primary);
      border-radius: 14px;
      padding: 24px !important;
      margin-top: 16px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      gap: 18px;
    `,parentContainer.insertBefore(accountStatus,listingsStatusOverview),parentContainer.insertBefore(adSpend,listingsStatusOverview.nextSibling),horizontalWrapper.remove()}const marketplacesRow=document.querySelector(".ad-spend-marketplaces-row"),headerCenter=document.querySelector(".ad-spend-header-center"),adSpendHeaderRow=document.querySelector(".ad-spend-header-row"),headerRightGroup=document.querySelector(".ad-spend-header-right-group"),headerRight=document.querySelector(".ad-spend-header-right");if(marketplacesRow&&headerCenter&&adSpendHeaderRow&&headerRightGroup&&headerRight){const customDivider=document.querySelector(".ad-spend-single-marketplace-divider");customDivider&&customDivider.remove(),headerCenter.style.display="",headerRightGroup.contains(headerCenter)||headerRightGroup.insertBefore(headerCenter,headerRight),headerCenter.querySelectorAll(".ad-spend-header-metric-group").forEach(group=>{group.classList.remove("single-marketplace")});const headerDivider=headerCenter.querySelector(".ad-spend-header-divider");headerDivider&&(headerDivider.style.cssText=`
        display: inline-block;
        width: 1px;
        height: 17.5px;
        background: var(--text-primary);
        opacity: 0.1;
        margin: 0 8px;
      `),headerCenter.querySelectorAll(".ad-spend-header-label").forEach(label=>{label.textContent.includes("Orders")?label.textContent="Total Orders:":label.textContent.includes("Ad Spend")&&(label.textContent="Total Ad Spend:")}),marketplacesRow.style.justifyContent="space-between",marketplacesRow.style.alignItems="center",marketplacesRow.style.gap="0"}[".ad-spend-marketplace-col-us",".ad-spend-marketplace-col-uk",".ad-spend-marketplace-col-de",".ad-spend-marketplace-col-fr",".ad-spend-marketplace-col-it",".ad-spend-marketplace-col-es",".ad-spend-marketplace-col-jp"].forEach(selector=>{const col=document.querySelector(selector);col&&(col.style.display="")}),document.querySelectorAll(".ad-spend-marketplace-divider").forEach(divider=>{divider.style.display=""}),document.querySelectorAll(".marketplaces-sales-row").forEach(row=>{row.style.display=""}),document.querySelectorAll(".sales-section-divider").forEach(divider=>{divider.style.display=""});const lifetimeInsightsCard=document.querySelector(".lifetime-insights-card-div"),reviewsCard=document.querySelector(".customer-reviews-card-div");if(lifetimeInsightsCard){const topDivider=lifetimeInsightsCard.querySelector(".sales-section-divider"),marketplacesRow2=lifetimeInsightsCard.querySelector(".marketplaces-sales-row");topDivider&&(topDivider.style.display=""),marketplacesRow2&&(marketplacesRow2.style.display=""),lifetimeInsightsCard.querySelectorAll(".lifetime-data-div .lifetime-data-item").forEach(item=>{const label=item.querySelector(".lifetime-data-label");label&&label.textContent&&label.textContent.trim().startsWith("Taxes (US")&&(item.style.display="")})}if(reviewsCard){const topDivider=reviewsCard.querySelector(".reviews-section-divider")||reviewsCard.querySelector(".sales-section-divider"),marketplacesRow2=reviewsCard.querySelector(".marketplaces-reviews-row");topDivider&&(topDivider.style.display=""),marketplacesRow2&&(marketplacesRow2.style.display="")}try{const storedTaxRate=localStorage.getItem("lifetimeTaxRate");storedTaxRate&&document.querySelectorAll(".lifetime-tax-rate").forEach(el=>{el.textContent=`${storedTaxRate}%`})}catch{}document.querySelectorAll(".listing-analytics-div").forEach(listing=>{listing.style.display=""}),document.querySelectorAll(".listing-section-divider").forEach(divider=>{divider.style.display=""}),resetAllSalesCardsToAllMarketplaces(),await applyEnhancedMarketplaceRestoration(),restoreAllSalesAnalytics(),await restoreFourSalesCardsToAllMarketplaces()}async function applyEnhancedMarketplaceRestoration(){try{enhancedAdSpendMarketplaceFiltering("all"),applyTableDataFiltering("all"),applyComprehensiveMarketplaceAggregation("all"),updateUIStateForMarketplaceFiltering("all"),updateChartForMarketplace("all")}catch{}}function resetAllSalesCardsToAllMarketplaces(){document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div").forEach((salesCard,cardIndex)=>{const marketplaceCols=salesCard.querySelectorAll(".marketplace-col"),marketplacesDiv=salesCard.querySelector(".marketplaces-div");marketplacesDiv&&marketplacesDiv.classList.remove("single-marketplace-active"),marketplaceCols.forEach(col=>{col.classList.remove("inactive"),col.classList.add("active")});const allMarketplacesIcon=salesCard.querySelector(".all-marketplaces img");allMarketplacesIcon&&(allMarketplacesIcon.src="./assets/all-marketplaces-active-ic.svg",allMarketplacesIcon.alt="All Marketplaces Active"),clearSearchStateForCardGlobal(cardIndex)}),typeof salesCardStates<"u"&&Object.keys(salesCardStates).forEach(cardIndex=>{salesCardStates[cardIndex].selectedMarketplace="all"})}function clearSearchStateForCardGlobal(cardIndex){const salesCard=document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div")[cardIndex];if(!salesCard)return;const searchInput=salesCard.querySelector(".search-input"),closeSearchIcon=salesCard.querySelector(".close-search-icon"),searchNoResults=salesCard.querySelector(".search-no-results");searchInput&&searchNoResults&&(searchInput.value="",closeSearchIcon&&(closeSearchIcon.style.opacity="0",closeSearchIcon.style.pointerEvents="none"),searchNoResults.style.display="none")}function applyFocusedMarketplaceFiltering(marketplace){const accountStatus=document.querySelector(".account-status"),adSpend=document.querySelector(".ad-spend");if(accountStatus&&adSpend){let horizontalWrapper=document.querySelector(".account-ad-spend-wrapper");horizontalWrapper||(horizontalWrapper=document.createElement("div"),horizontalWrapper.className="account-ad-spend-wrapper",horizontalWrapper.style.cssText=`
        display: flex;
        gap: 16px;
        width: 100%;
        min-width: 1024px !important;
      `,accountStatus.parentNode.insertBefore(horizontalWrapper,accountStatus)),accountStatus.parentNode!==horizontalWrapper&&horizontalWrapper.appendChild(accountStatus),adSpend.parentNode!==horizontalWrapper&&horizontalWrapper.appendChild(adSpend),accountStatus.style.cssText+=`
    flex: 0 0 calc(72% - 11.52px);
    margin-top: 0;
    width: auto;
    min-width: 0 !important;
  `,adSpend.style.cssText+=`
    flex: 0 0 calc(28% - 4.48px);
    margin-top: 0;
    width: auto;
    min-width: 0 !important;
  `}enhancedAdSpendMarketplaceFiltering(marketplace),document.querySelectorAll(".marketplaces-sales-row").forEach(row=>{row.style.display="none"}),document.querySelectorAll(".sales-section-divider, .reviews-section-divider").forEach(divider=>{divider.style.display="none"});const lifetimeInsightsCard=document.querySelector(".lifetime-insights-card-div"),reviewsCard=document.querySelector(".customer-reviews-card-div");if(lifetimeInsightsCard){const dividers=lifetimeInsightsCard.querySelectorAll(".sales-section-divider"),marketplacesRow=lifetimeInsightsCard.querySelector(".marketplaces-sales-row");marketplacesRow&&(marketplacesRow.style.display="none"),dividers.length>1&&(dividers[1].style.display="");const lifetimeItems=lifetimeInsightsCard.querySelectorAll(".lifetime-data-div .lifetime-data-item");let taxesItem=null;if(lifetimeItems.forEach(item=>{const label=item.querySelector(".lifetime-data-label");label&&label.textContent&&label.textContent.trim().startsWith("Taxes (US")&&(taxesItem=item)}),taxesItem){const isUS=typeof marketplace=="string"&&marketplace.toLowerCase()==="us",isAll=marketplace==="all";taxesItem.style.display=isAll||isUS?"":"none"}}if(reviewsCard){const dividers=reviewsCard.querySelectorAll(".reviews-section-divider, .sales-section-divider"),marketplacesRow=reviewsCard.querySelector(".marketplaces-reviews-row");marketplacesRow&&(marketplacesRow.style.display="none"),dividers.length>1&&(dividers[1].style.display="")}enhancedMarketplaceListingFilter(marketplace),filterListingsByMarketplaceWithAnalytics(marketplace),filterFourSalesCardsByMarketplace(marketplace),applyComprehensiveMarketplaceAggregation(marketplace),updateUIStateForMarketplaceFiltering(marketplace)}function filterListingsByMarketplaceWithAnalytics(marketplace){document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div").forEach((salesCard,cardIndex)=>{const allListings=salesCard.querySelectorAll(".listing-analytics-div"),allDividers=salesCard.querySelectorAll(".listing-section-divider");let visibleListingsCount=0,filteredListings=[];allListings.forEach((listing,index)=>{const marketplaceFlag=listing.querySelector(".listing-marketplace-flag");let shouldShow=!1;if(marketplace==="all")shouldShow=!0;else if(marketplaceFlag){const flagSrc=marketplaceFlag.src;shouldShow=checkIfListingMatchesMarketplace(flagSrc,marketplace)}else shouldShow=!1;if(shouldShow?(listing.style.display="",listing.classList.remove("marketplace-filtered"),visibleListingsCount++,filteredListings.push(listing)):(listing.style.display="none",listing.classList.add("marketplace-filtered")),index<allDividers.length){const divider=allDividers[index];divider&&(divider.style.display=shouldShow?"":"none")}}),manageDividersForFilteredListings(allListings,allDividers,filteredListings),applyMarketplaceDataFiltering(salesCard,marketplace),updateListingUIForMarketplace(salesCard,marketplace,visibleListingsCount),setTimeout(()=>{updateNewTabCount(salesCard)},15)}),applyTableDataFiltering(marketplace)}function manageDividersForFilteredListings(allListings,allDividers,filteredListings){allDividers.forEach(divider=>{divider.style.display="none"}),filteredListings.length>1&&filteredListings.forEach((listing,index)=>{if(index<filteredListings.length-1){const listingIndex=Array.from(allListings).indexOf(listing);listingIndex<allDividers.length&&allDividers[listingIndex]&&(allDividers[listingIndex].style.display="")}})}function updateListingUIForMarketplace(salesCard,marketplace,visibleCount){salesCard.querySelectorAll(".listing-count, .listings-count").forEach(element=>{element.textContent=visibleCount.toString()});const noListingsMessage=salesCard.querySelector(".no-listings-message");noListingsMessage&&(visibleCount===0?(noListingsMessage.style.display="",noListingsMessage.textContent=marketplace==="all"?"No listings found":`No listings found for ${marketplace.toUpperCase()}`):noListingsMessage.style.display="none");const filterIndicator=salesCard.querySelector(".marketplace-filter-indicator");filterIndicator&&filterIndicator.remove()}function applyTableDataFiltering(marketplace){document.querySelectorAll(".data-table, .listings-table, .sales-table").forEach(table=>{table.querySelectorAll("tbody tr, .table-row").forEach(row=>{const marketplaceCell=row.querySelector(".marketplace-cell, [data-marketplace]");let shouldShow=!0;marketplace!=="all"&&marketplaceCell&&(shouldShow=(marketplaceCell.dataset.marketplace||marketplaceCell.textContent.toLowerCase().trim())===marketplace.toLowerCase()),row.style.display=shouldShow?"":"none"})})}function calculateComprehensiveMarketplaceAggregation(marketplace){const aggregation={marketplace,totalSales:0,totalRoyalties:0,totalReturns:0,totalListings:0,averageSalesPerListing:0,averageRoyaltiesPerListing:0,marketplaceBreakdown:{},cardBreakdown:{},timeSeriesData:{today:{sales:0,royalties:0,returns:0},yesterday:{sales:0,royalties:0,returns:0},currentMonth:{sales:0,royalties:0,returns:0},lastMonth:{sales:0,royalties:0,returns:0},currentYear:{sales:0,royalties:0,returns:0},lastYear:{sales:0,royalties:0,returns:0}},adSpendData:{totalSpend:0,totalOrders:0,averageOrderValue:0,costPerSale:0}};return document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div").forEach((salesCard,cardIndex)=>{const cardType=getCardTypeFromElement(salesCard),cardData=calculateMarketplaceSpecificAnalytics(salesCard,marketplace);aggregation.cardBreakdown[cardType]=cardData,aggregation.totalSales+=cardData.totalUnits,aggregation.totalRoyalties+=cardData.totalRoyalties,aggregation.totalReturns+=cardData.totalReturned,aggregation.totalListings+=cardData.totalListings,updateTimeSeriesAggregation(aggregation.timeSeriesData,cardType,cardData),Object.keys(cardData.marketplaceBreakdown).forEach(mp=>{aggregation.marketplaceBreakdown[mp]||(aggregation.marketplaceBreakdown[mp]={units:0,royalties:0,returned:0,listings:0});const mpData=cardData.marketplaceBreakdown[mp];aggregation.marketplaceBreakdown[mp].units+=mpData.units,aggregation.marketplaceBreakdown[mp].royalties+=mpData.royalties,aggregation.marketplaceBreakdown[mp].returned+=mpData.returned,aggregation.marketplaceBreakdown[mp].listings+=mpData.listings})}),aggregation.totalListings>0&&(aggregation.averageSalesPerListing=aggregation.totalSales/aggregation.totalListings,aggregation.averageRoyaltiesPerListing=aggregation.totalRoyalties/aggregation.totalListings),calculateAdSpendAggregation(aggregation.adSpendData,marketplace),aggregation.adSpendData.totalSpend>0&&aggregation.totalSales>0&&(aggregation.adSpendData.costPerSale=aggregation.adSpendData.totalSpend/aggregation.totalSales),aggregation.adSpendData.totalOrders>0&&aggregation.totalRoyalties>0&&(aggregation.adSpendData.averageOrderValue=aggregation.totalRoyalties/aggregation.adSpendData.totalOrders),aggregation}function getCardTypeFromElement(salesCard){return salesCard.classList.contains("todays-sales-card-div")?"today":salesCard.classList.contains("yesterdays-sales-card-div")?"yesterday":salesCard.classList.contains("current-month-card-div")?"currentMonth":salesCard.classList.contains("last-month-card-div")?"lastMonth":salesCard.classList.contains("current-year-card-div")?"currentYear":salesCard.classList.contains("last-year-card-div")?"lastYear":"unknown"}function updateTimeSeriesAggregation(timeSeriesData,cardType,cardData){timeSeriesData[cardType]&&(timeSeriesData[cardType].sales=cardData.totalUnits,timeSeriesData[cardType].royalties=cardData.totalRoyalties,timeSeriesData[cardType].returns=cardData.totalReturned)}function calculateAdSpendAggregation(adSpendData,marketplace){if(marketplace==="all")document.querySelectorAll(".ad-spend-marketplace-col").forEach(col=>{if(window.getComputedStyle(col).display!=="none"){const spendValue=col.querySelector(".ad-spend-value.ad-spend-currency"),ordersValue=col.querySelector(".ad-spend-orders");if(spendValue&&ordersValue){const spendText=spendValue.textContent.replace(/[^\d.,]/g,""),ordersText=ordersValue.textContent.replace(/[^\d]/g,"");adSpendData.totalSpend+=parseFloat(spendText.replace(",",""))||0,adSpendData.totalOrders+=parseInt(ordersText)||0}}});else{const marketplaceCol=document.querySelector(`.ad-spend-marketplace-col-${marketplace}`);if(marketplaceCol){const spendValue=marketplaceCol.querySelector(".ad-spend-value.ad-spend-currency"),ordersValue=marketplaceCol.querySelector(".ad-spend-orders");if(spendValue&&ordersValue){const spendText=spendValue.textContent.replace(/[^\d.,]/g,""),ordersText=ordersValue.textContent.replace(/[^\d]/g,"");adSpendData.totalSpend=parseFloat(spendText.replace(",",""))||0,adSpendData.totalOrders=parseInt(ordersText)||0}}}}function applyComprehensiveMarketplaceAggregation(marketplace){const aggregation=calculateComprehensiveMarketplaceAggregation(marketplace);return updateDashboardSummaryElements(aggregation),updatePerformanceMetrics(aggregation),aggregation}function updateDashboardSummaryElements(aggregation){const totalSalesElement=document.querySelector(".total-sales-summary, .dashboard-total-sales");totalSalesElement&&(totalSalesElement.textContent=formatNumberWithCommas(aggregation.totalSales));const totalRoyaltiesElement=document.querySelector(".total-royalties-summary, .dashboard-total-royalties");if(totalRoyaltiesElement){const currencySymbol=aggregation.marketplace==="all"?"$":getCurrencySymbolForMarketplace(aggregation.marketplace);totalRoyaltiesElement.textContent=formatCurrencyValue(aggregation.totalRoyalties,currencySymbol)}const totalListingsElement=document.querySelector(".total-listings-summary, .dashboard-total-listings");totalListingsElement&&(totalListingsElement.textContent=formatNumberWithCommas(aggregation.totalListings))}function updateChartsWithAggregatedData(aggregation){}function updatePerformanceMetrics(aggregation){const avgSalesElement=document.querySelector(".avg-sales-per-listing");avgSalesElement&&(avgSalesElement.textContent=aggregation.averageSalesPerListing.toFixed(1));const costPerSaleElement=document.querySelector(".cost-per-sale");if(costPerSaleElement&&aggregation.adSpendData.costPerSale>0){const currencySymbol=aggregation.marketplace==="all"?"$":getCurrencySymbolForMarketplace(aggregation.marketplace);costPerSaleElement.textContent=formatCurrencyValue(aggregation.adSpendData.costPerSale,currencySymbol)}const avgOrderValueElement=document.querySelector(".avg-order-value");if(avgOrderValueElement&&aggregation.adSpendData.averageOrderValue>0){const currencySymbol=aggregation.marketplace==="all"?"$":getCurrencySymbolForMarketplace(aggregation.marketplace);avgOrderValueElement.textContent=formatCurrencyValue(aggregation.adSpendData.averageOrderValue,currencySymbol)}}function updateUIStateForMarketplaceFiltering(marketplace){updateMarketplaceColumnStates(marketplace),updateMarketplaceFilterIndicators(marketplace),updateDashboardLayoutForMarketplace(marketplace),updateAccessibilityAttributes(marketplace)}function updateMarketplaceColumnStates(marketplace){document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div").forEach(salesCard=>{const marketplaceCols=salesCard.querySelectorAll(".marketplace-col"),marketplacesDiv=salesCard.querySelector(".marketplaces-div");marketplace==="all"?(marketplacesDiv&&marketplacesDiv.classList.remove("single-marketplace-active"),marketplaceCols.forEach(col=>{col.classList.remove("inactive","marketplace-filtered"),col.classList.add("active"),col.style.display="",col.style.opacity=""}),salesCard.querySelectorAll(".marketplace-divider").forEach(divider=>{divider.style.display=""})):(marketplacesDiv&&marketplacesDiv.classList.add("single-marketplace-active"),marketplaceCols.forEach(col=>{const isSelectedMarketplace=col.classList.contains(marketplace),isAllMarketplaces=col.classList.contains("all-marketplaces");isSelectedMarketplace?(col.classList.remove("inactive","marketplace-filtered"),col.classList.add("active"),col.style.display="flex",col.style.opacity="1"):(col.classList.add("inactive","marketplace-filtered"),col.classList.remove("active"),col.style.display="none",col.style.opacity="0.5")}),salesCard.querySelectorAll(".marketplace-divider").forEach(divider=>{divider.style.display="none"}))})}function updateMarketplaceFilterIndicators(marketplace){const marketplaceFocusButton=document.querySelector(".marketplace-focus-button, .marketplace-dropdown-button");if(marketplaceFocusButton){const buttonText=marketplace==="all"?"All Marketplaces":marketplace.toUpperCase(),textElement=marketplaceFocusButton.querySelector(".button-text, .dropdown-text");textElement&&(textElement.textContent=buttonText)}const filterStatusElement=document.querySelector(".marketplace-filter-status");filterStatusElement&&(marketplace==="all"?(filterStatusElement.textContent="Showing all marketplaces",filterStatusElement.classList.remove("filtered")):(filterStatusElement.textContent=`Filtered by: ${marketplace.toUpperCase()}`,filterStatusElement.classList.add("filtered"))),updateChartTitlesForMarketplace(marketplace)}function updateDashboardLayoutForMarketplace(marketplace){const dashboardContainer=document.querySelector(".dashboard-container, .main-content");dashboardContainer&&(marketplace==="all"?(dashboardContainer.classList.remove("single-marketplace-mode"),dashboardContainer.classList.add("all-marketplaces-mode")):(dashboardContainer.classList.remove("all-marketplaces-mode"),dashboardContainer.classList.add("single-marketplace-mode"),dashboardContainer.setAttribute("data-filtered-marketplace",marketplace)))}function updateAccessibilityAttributes(marketplace){document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div").forEach(salesCard=>{const cardTitle=salesCard.querySelector(".card-title, .sales-card-title");if(cardTitle){const originalTitle=cardTitle.textContent,ariaLabel=marketplace==="all"?`${originalTitle} - All Marketplaces`:`${originalTitle} - ${marketplace.toUpperCase()} Only`;cardTitle.setAttribute("aria-label",ariaLabel)}}),document.querySelectorAll(".marketplace-col").forEach(col=>{const isVisible=window.getComputedStyle(col).display!=="none";col.setAttribute("aria-hidden",!isVisible)})}function updateChartTitlesForMarketplace(marketplace){document.querySelectorAll('[id$="-chart-container"]').forEach(container=>{const titleElement=container.querySelector(".chart-title, .chart-header-title");if(titleElement){const originalTitle=titleElement.dataset.originalTitle||titleElement.textContent;titleElement.dataset.originalTitle||(titleElement.dataset.originalTitle=originalTitle),marketplace==="all"?titleElement.textContent=originalTitle:titleElement.textContent=`${originalTitle} - ${marketplace.toUpperCase()}`}})}function validateMarketplaceFiltering(marketplace){const validation={marketplace,timestamp:new Date().toISOString(),passed:!0,errors:[],warnings:[],results:{salesCards:{passed:!1,details:{}},adSpend:{passed:!1,details:{}},listings:{passed:!1,details:{}},uiState:{passed:!1,details:{}}}};try{validation.results.salesCards=validateSalesCardsFiltering(marketplace),validation.results.adSpend=validateAdSpendFiltering(marketplace),validation.results.listings=validateListingsFiltering(marketplace),validation.results.uiState=validateUIState(marketplace),validation.passed=Object.values(validation.results).every(result=>result.passed),Object.values(validation.results).forEach(result=>{result.errors&&validation.errors.push(...result.errors),result.warnings&&validation.warnings.push(...result.warnings)})}catch(error){validation.passed=!1,validation.errors.push(`Validation failed with error: ${error.message}`)}const status=validation.passed?"\u2705 PASSED":"\u274C FAILED";return validation}async function testMarketplaceFiltering(marketplace){const testResults={marketplace,timestamp:new Date().toISOString(),success:!1,duration:0,validationResults:null,errors:[]},startTime=Date.now();try{marketplace==="all"?await showAllMarketplaceElements():applyFocusedMarketplaceFiltering(marketplace),await new Promise(resolve=>setTimeout(resolve,100)),testResults.validationResults=validateMarketplaceFiltering(marketplace),testResults.success=testResults.validationResults.passed,testResults.validationResults.errors.length>0&&testResults.errors.push(...testResults.validationResults.errors)}catch(error){testResults.success=!1,testResults.errors.push(`Test failed with error: ${error.message}`)}testResults.duration=Date.now()-startTime;const status=testResults.success?"\u2705 PASSED":"\u274C FAILED";return testResults}function validateSalesCardsFiltering(marketplace){const result={passed:!0,errors:[],warnings:[],details:{}};return document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div").forEach((salesCard,index)=>{const cardValidation={visibleColumns:0,hiddenColumns:0,dataConsistency:!0};salesCard.querySelectorAll(".marketplace-col").forEach(col=>{const isVisible=window.getComputedStyle(col).display!=="none",isSelectedMarketplace=col.classList.contains(marketplace);isVisible?(cardValidation.visibleColumns++,marketplace!=="all"&&!isSelectedMarketplace&&(result.errors.push(`Card ${index}: Incorrect column visible for marketplace ${marketplace}`),result.passed=!1)):cardValidation.hiddenColumns++}),marketplace==="all"?cardValidation.visibleColumns===0&&result.warnings.push(`Card ${index}: No columns visible in 'all' mode`):cardValidation.visibleColumns!==1&&(result.errors.push(`Card ${index}: Expected 1 visible column for single marketplace, found ${cardValidation.visibleColumns}`),result.passed=!1),result.details[`card_${index}`]=cardValidation}),result}function validateAdSpendFiltering(marketplace){const result={passed:!0,errors:[],warnings:[],details:{}},adSpendSection=document.querySelector(".ad-spend");if(!adSpendSection)return result.warnings.push("Ad spend section not found"),result;const adSpendCols=adSpendSection.querySelectorAll(".ad-spend-marketplace-col");let visibleCols=0;return adSpendCols.forEach(col=>{window.getComputedStyle(col).display!=="none"&&visibleCols++}),marketplace==="all"?visibleCols===0&&result.warnings.push("No ad spend columns visible in all marketplaces mode"):visibleCols!==1&&(result.errors.push(`Expected 1 visible ad spend column for single marketplace, found ${visibleCols}`),result.passed=!1),result.details.visibleColumns=visibleCols,result}function validateListingsFiltering(marketplace){const result={passed:!0,errors:[],warnings:[],details:{}};return document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div").forEach((salesCard,cardIndex)=>{const listings=salesCard.querySelectorAll(".listing-analytics-div");let visibleListings=0;listings.forEach(listing=>{window.getComputedStyle(listing).display!=="none"&&visibleListings++}),result.details[`card_${cardIndex}_listings`]=visibleListings}),result}function validateUIState(marketplace){const result={passed:!0,errors:[],warnings:[],details:{}},dashboardContainer=document.querySelector(".dashboard-container, .main-content");if(dashboardContainer){const hasAllMode=dashboardContainer.classList.contains("all-marketplaces-mode"),hasSingleMode=dashboardContainer.classList.contains("single-marketplace-mode");marketplace==="all"&&!hasAllMode?result.warnings.push("Dashboard container missing all-marketplaces-mode class"):marketplace!=="all"&&!hasSingleMode&&result.warnings.push("Dashboard container missing single-marketplace-mode class")}return result}function updateListingDividersForCard(salesCard){const allListings=salesCard.querySelectorAll(".listing-analytics-div");salesCard.querySelectorAll(".listing-section-divider").forEach(divider=>{divider.style.display="none"});const visibleListings=Array.from(allListings).filter(listing=>window.getComputedStyle(listing).display!=="none");visibleListings.forEach((listing,index)=>{if(index<visibleListings.length-1){let nextElement=listing.nextElementSibling;for(;nextElement;){if(nextElement.classList.contains("listing-section-divider")){nextElement.style.display="";break}nextElement=nextElement.nextElementSibling}}})}function applyMarketplaceDataFiltering(salesCard,marketplace){const marketplaceCols=salesCard.querySelectorAll(".marketplace-col"),marketplacesDiv=salesCard.querySelector(".marketplaces-div");marketplacesDiv&&marketplacesDiv.classList.add("single-marketplace-active"),marketplaceCols.forEach(col=>{const isSelectedMarketplace=col.classList.contains(marketplace),isAllMarketplaces=col.classList.contains("all-marketplaces");isSelectedMarketplace?(col.classList.remove("inactive"),col.classList.add("active"),col.style.display="flex"):(col.classList.add("inactive"),col.classList.remove("active"),col.style.display="none")});const marketplaceAnalytics=calculateMarketplaceSpecificAnalytics(salesCard,marketplace),salesCount=salesCard.querySelector(".sales-count");salesCount&&(salesCount.textContent=formatNumberWithCommas(marketplaceAnalytics.totalUnits),salesCount.classList.toggle("zero",marketplaceAnalytics.totalUnits===0)),updateAllPercentageCalculations(salesCard,marketplace),updateNoSalesStateForCard(salesCard,marketplaceAnalytics.totalUnits),updateMarketplaceColumnData(salesCard,marketplace,marketplaceAnalytics)}function updateMarketplaceColumnData(salesCard,marketplace,analytics){const selectedMarketplaceCol=salesCard.querySelector(`.marketplace-col.${marketplace}`);if(selectedMarketplaceCol&&analytics.marketplaceBreakdown[marketplace]){const marketplaceData=analytics.marketplaceBreakdown[marketplace],salesCountElement=selectedMarketplaceCol.querySelector(".marketplace-total-sales-count");salesCountElement&&(salesCountElement.textContent=formatNumberWithCommas(marketplaceData.units),salesCountElement.classList.toggle("zero",marketplaceData.units===0));const royaltiesElement=selectedMarketplaceCol.querySelector(".marketplace-total-earned-royalties");if(royaltiesElement){const currencySymbol=getCurrencySymbolForMarketplace(marketplace),formattedRoyalties=formatCurrencyValue(marketplaceData.royalties,currencySymbol);royaltiesElement.textContent=formattedRoyalties,royaltiesElement.classList.toggle("zero",marketplaceData.royalties===0),royaltiesElement.classList.toggle("negative",marketplaceData.royalties<0)}const returnedElement=selectedMarketplaceCol.querySelector(".marketplace-total-returned-units");if(returnedElement){const formattedReturned=marketplaceData.returned===0?"(0)":marketplaceData.returned>0?`(${formatNumberWithCommas(marketplaceData.returned)})`:`(-${formatNumberWithCommas(Math.abs(marketplaceData.returned))})`;returnedElement.textContent=formattedReturned,returnedElement.classList.toggle("zero",marketplaceData.returned===0),returnedElement.classList.toggle("negative",marketplaceData.returned<0)}}}function getCurrencySymbolForMarketplace(marketplace){return{us:"$",uk:"\xA3",de:"\u20AC",fr:"\u20AC",it:"\u20AC",es:"\u20AC",jp:"\xA5"}[marketplace.toLowerCase()]||"$"}function formatCurrencyValue(value,symbol){if(value===0)return`${symbol}0.00`;const formattedValue=Math.abs(value).toLocaleString("en-US",{minimumFractionDigits:1,maximumFractionDigits:1});return value<0?`-${symbol}${formattedValue}`:`${symbol}${formattedValue}`}function enhancedMarketplaceListingFilter(marketplace){document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div").forEach((salesCard,cardIndex)=>{const allListings=salesCard.querySelectorAll(".listing-analytics-div"),allDividers=salesCard.querySelectorAll(".listing-section-divider");let visibleListingsCount=0;allListings.forEach((listing,index)=>{const marketplaceFlag=listing.querySelector(".listing-marketplace-flag");let shouldShow=!1;if(marketplaceFlag){const flagSrc=marketplaceFlag.src;shouldShow=checkIfListingMatchesMarketplace(flagSrc,marketplace)}if(shouldShow?(listing.style.display="",visibleListingsCount++):listing.style.display="none",index<allDividers.length){const divider=allDividers[index];divider&&(divider.style.display=shouldShow?"":"none")}});const visibleListings=Array.from(allListings).filter(listing=>window.getComputedStyle(listing).display!=="none");if(visibleListings.length>0){const lastVisibleIndex=Array.from(allListings).indexOf(visibleListings[visibleListings.length-1]);lastVisibleIndex<allDividers.length&&allDividers[lastVisibleIndex]&&(allDividers[lastVisibleIndex].style.display="none")}})}function enhancedAdSpendMarketplaceFiltering(marketplace){document.querySelector(".ad-spend")&&(marketplace==="all"?restoreAllAdSpendMarketplaces():applySingleMarketplaceAdSpendFiltering(marketplace))}function applySingleMarketplaceAdSpendFiltering(marketplace){const adSpendColMapping={us:".ad-spend-marketplace-col-us",uk:".ad-spend-marketplace-col-uk",de:".ad-spend-marketplace-col-de",fr:".ad-spend-marketplace-col-fr",it:".ad-spend-marketplace-col-it",es:".ad-spend-marketplace-col-es",jp:".ad-spend-marketplace-col-jp"},allAdSpendCols=Object.values(adSpendColMapping),selectedAdSpendCol=adSpendColMapping[marketplace];if(!selectedAdSpendCol)return;const marketplacesRow=document.querySelector(".ad-spend-marketplaces-row"),headerCenter=document.querySelector(".ad-spend-header-center");marketplacesRow&&headerCenter&&(headerCenter.style.display="none",marketplacesRow.style.cssText=`
      justify-content: center;
      align-items: center;
      gap: 0;
      display: flex;
    `),allAdSpendCols.forEach(selector=>{const col=document.querySelector(selector);col&&(selector===selectedAdSpendCol?(col.style.display="flex",col.style.margin="0 auto"):col.style.display="none")}),document.querySelectorAll(".ad-spend-marketplace-divider").forEach(divider=>{divider.style.display="none"}),updateAdSpendDataForMarketplace(marketplace)}function restoreAllAdSpendMarketplaces(){const allAdSpendCols=Object.values({us:".ad-spend-marketplace-col-us",uk:".ad-spend-marketplace-col-uk",de:".ad-spend-marketplace-col-de",fr:".ad-spend-marketplace-col-fr",it:".ad-spend-marketplace-col-it",es:".ad-spend-marketplace-col-es",jp:".ad-spend-marketplace-col-jp"}),marketplacesRow=document.querySelector(".ad-spend-marketplaces-row"),headerCenter=document.querySelector(".ad-spend-header-center");marketplacesRow&&headerCenter&&(headerCenter.style.display="",marketplacesRow.style.cssText=`
      justify-content: space-between;
      align-items: center;
      gap: 8px;
      display: flex;
    `),allAdSpendCols.forEach(selector=>{const col=document.querySelector(selector);col&&(col.style.display="",col.style.margin="")}),document.querySelectorAll(".ad-spend-marketplace-divider").forEach(divider=>{divider.style.display=""}),updateAdSpendDataForMarketplace("all")}function updateAdSpendDataForMarketplace(marketplace){const spendElement=document.querySelector(".ad-spend-header-value"),ordersElement=document.querySelectorAll(".ad-spend-header-value")[1];if(marketplace==="all"){let totalSpend=0,totalOrders=0;document.querySelectorAll(".ad-spend-marketplace-col").forEach(col=>{if(window.getComputedStyle(col).display!=="none"){const spendValue=col.querySelector(".ad-spend-value.ad-spend-currency"),ordersValue=col.querySelector(".ad-spend-orders");if(spendValue&&ordersValue){const spendText=spendValue.textContent.replace(/[^\d.,]/g,""),ordersText=ordersValue.textContent.replace(/[^\d]/g,"");totalSpend+=parseFloat(spendText.replace(",",""))||0,totalOrders+=parseInt(ordersText)||0}}}),spendElement&&(spendElement.textContent=`$${totalSpend.toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})}`),ordersElement&&(ordersElement.textContent=totalOrders.toLocaleString())}else{const marketplaceColSelector=`.ad-spend-marketplace-col-${marketplace}`,marketplaceCol=document.querySelector(marketplaceColSelector);if(marketplaceCol){const spendValue=marketplaceCol.querySelector(".ad-spend-value.ad-spend-currency"),ordersValue=marketplaceCol.querySelector(".ad-spend-orders");spendValue&&ordersValue&&spendElement&&ordersElement&&(spendElement.textContent=spendValue.textContent,ordersElement.textContent=ordersValue.textContent.replace(/[()]/g,""))}else spendElement&&(spendElement.textContent="$0.00"),ordersElement&&(ordersElement.textContent="0")}}function updateSalesAnalyticsForMarketplace(salesCard,marketplace,visibleListingsCount){if(!salesCard.querySelector(".sales-analytics-div"))return;const visibleListings=Array.from(salesCard.querySelectorAll(".listing-analytics-div")).filter(listing=>window.getComputedStyle(listing).display!=="none");let totalRoyalties=0,totalUnits=0,totalReturned=0;visibleListings.forEach(listing=>{const royaltiesBadge=listing.querySelector(".royalties-badge span:last-child");if(royaltiesBadge){const royaltiesText=royaltiesBadge.textContent.trim(),royaltiesValue=parseRoyaltiesToUSD(royaltiesText);isNaN(royaltiesValue)||(totalRoyalties+=royaltiesValue)}const unitsBadge=listing.querySelector(".order-units-badge span:last-child");if(unitsBadge){const unitsText=unitsBadge.textContent.trim(),unitsValue=parseInt(unitsText)||0;totalUnits+=unitsValue}const returnedBadge=listing.querySelector(".returned-units-badge span:last-child");if(returnedBadge){const returnedText=returnedBadge.textContent.trim(),returnedValue=parseInt(returnedText)||0;totalReturned+=returnedValue}});const salesCount=salesCard.querySelector(".sales-count");salesCount&&(salesCount.textContent=formatNumberWithCommas(totalUnits)),updateNoSalesStateForCard(salesCard,totalUnits);const analyticsDiv=salesCard.querySelector(".analytics-div");if(analyticsDiv){const royaltiesValue=analyticsDiv.querySelector(".metric-value.royalties");if(royaltiesValue){const formattedRoyalties=totalRoyalties>=0?`$${totalRoyalties.toFixed(2)}`:`-$${Math.abs(totalRoyalties).toFixed(2)}`;royaltiesValue.textContent=formattedRoyalties,royaltiesValue.classList.remove("negative","zero"),totalRoyalties<0?royaltiesValue.classList.add("negative"):totalRoyalties===0&&royaltiesValue.classList.add("zero")}const unitsValue=analyticsDiv.querySelector(".metric-col:nth-child(2) .metric-value");unitsValue&&(unitsValue.textContent=formatNumberWithCommas(totalUnits),unitsValue.classList.toggle("zero",totalUnits===0));const returnedValue=analyticsDiv.querySelector(".metric-value.returned");if(returnedValue)if(totalReturned===0)returnedValue.textContent="0",returnedValue.classList.add("zero");else{const returnedPercentage=totalUnits>0?(Math.abs(totalReturned)/totalUnits*100).toFixed(1):"0.0";returnedValue.textContent=`${formatReturnedMetric(totalReturned)} ${returnedPercentage}%`,returnedValue.classList.remove("zero")}}}function updateNoSalesStateForCard(salesCard,salesCountOrVisibleCount){const noSalesState=salesCard.querySelector(".no-sales-state");if(salesCountOrVisibleCount===0){noSalesState&&(noSalesState.style.display="flex");const salesFilterDiv=salesCard.querySelector(".sales-filter-div");salesFilterDiv&&(salesFilterDiv.style.display="none");const searchDiv=salesCard.querySelector(".search-div");searchDiv&&(searchDiv.style.display="none")}else{noSalesState&&(noSalesState.style.display="none");const salesFilterDiv=salesCard.querySelector(".sales-filter-div");salesFilterDiv&&(salesFilterDiv.style.display="flex");const searchDiv=salesCard.querySelector(".search-div");searchDiv&&(searchDiv.style.display="")}}function restoreAllSalesAnalytics(){const salesCards=document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div");salesCards.forEach(salesCard=>{if(window.SnapLoader){const overlay=window.SnapLoader.showOverlay(salesCard,{text:"Updating sales data...",id:"sales-card-loader",size:"medium"});overlay&&(overlay.classList.add("snap-loader-compact"),salesCard.firstChild!==overlay&&salesCard.insertBefore(overlay,salesCard.firstChild))}}),salesCards.forEach((salesCard,cardIndex)=>{const analytics=calculateComprehensiveAnalytics(salesCard),salesCount=salesCard.querySelector(".sales-count");salesCount&&(salesCount.textContent=analytics.totalUnits.toString()),updateNoSalesStateForCard(salesCard,analytics.totalUnits),updateAllPercentageCalculations(salesCard,"all"),updateAllMarketplaceColumns(salesCard,analytics)}),setTimeout(()=>{salesCards.forEach(salesCard=>{window.SnapLoader&&window.SnapLoader.hideOverlay(salesCard)}),window.updateAllSalesCardsPadding&&window.updateAllSalesCardsPadding()},300)}function updateCalculatedAnalyticsValues(salesCard,totalRoyalties,totalUnits,totalReturned){const analyticsDiv=salesCard.querySelector(".analytics-div");if(!analyticsDiv)return;const royaltiesValue=analyticsDiv.querySelector(".metric-value.royalties");if(royaltiesValue){const formattedRoyalties=totalRoyalties>=0?`$${totalRoyalties.toFixed(2)}`:`-$${Math.abs(totalRoyalties).toFixed(2)}`;royaltiesValue.textContent=formattedRoyalties,royaltiesValue.classList.remove("negative","zero"),totalRoyalties<0?royaltiesValue.classList.add("negative"):totalRoyalties===0&&royaltiesValue.classList.add("zero")}const unitsValue=analyticsDiv.querySelector(".metric-col:nth-child(2) .metric-value");unitsValue&&(unitsValue.textContent=totalUnits.toString(),unitsValue.classList.toggle("zero",totalUnits===0));const returnedValue=analyticsDiv.querySelector(".metric-value.returned");if(returnedValue)if(totalReturned===0)returnedValue.textContent="0",returnedValue.classList.add("zero");else{const returnedPercentage=totalUnits>0?(Math.abs(totalReturned)/totalUnits*100).toFixed(1):"0.0";returnedValue.textContent=`${formatReturnedMetric(totalReturned)} ${returnedPercentage}%`,returnedValue.classList.remove("zero")}}function updateAllMarketplacesTotals(salesCard,totalRoyalties,totalUnits,totalReturned){const allMarketplacesCol=salesCard.querySelector(".marketplace-col.all-marketplaces");if(!allMarketplacesCol)return;const salesCountElement=allMarketplacesCol.querySelector(".marketplace-total-sales-count");salesCountElement&&(salesCountElement.textContent=totalUnits.toString(),salesCountElement.classList.remove("zero"),totalUnits===0&&salesCountElement.classList.add("zero"));const royaltiesElement=allMarketplacesCol.querySelector(".marketplace-total-earned-royalties");if(royaltiesElement){const formattedRoyalties=totalRoyalties===0?"$0.00":`$${totalRoyalties.toFixed(2)}`;royaltiesElement.textContent=formattedRoyalties,royaltiesElement.classList.remove("zero","negative"),totalRoyalties===0?royaltiesElement.classList.add("zero"):totalRoyalties<0&&royaltiesElement.classList.add("negative")}const returnedElement=allMarketplacesCol.querySelector(".marketplace-total-returned-units");if(returnedElement){const formattedReturned=formatReturnedUnits(totalReturned);returnedElement.textContent=formattedReturned,returnedElement.classList.remove("zero"),totalReturned===0&&returnedElement.classList.add("zero")}}function calculateComprehensiveAnalytics(salesCard){const allListings=salesCard.querySelectorAll(".listing-analytics-div"),analytics={totalRoyalties:0,totalUnits:0,totalReturned:0,totalCancelled:0,totalNew:0,totalAds:0,marketplaceBreakdown:{all:{units:0,royalties:0,returned:0},us:{units:0,royalties:0,returned:0},uk:{units:0,royalties:0,returned:0},de:{units:0,royalties:0,returned:0},fr:{units:0,royalties:0,returned:0},it:{units:0,royalties:0,returned:0},es:{units:0,royalties:0,returned:0},jp:{units:0,royalties:0,returned:0}}};return allListings.forEach((listing,index)=>{if(window.getComputedStyle(listing).display!=="none"){const royaltiesValue=extractRoyaltiesFromListing(listing),unitsValue=extractUnitsFromListing(listing),returnedValue=extractReturnedFromListing(listing);analytics.totalRoyalties+=royaltiesValue,analytics.totalUnits+=unitsValue,analytics.totalReturned+=returnedValue,analytics.totalCancelled+=extractCancelledFromListing(listing),analytics.totalNew+=extractNewFromListing(listing);const adSales=extractAdFromListing(listing);analytics.totalAds+=adSales;const marketplace=getListingMarketplace(listing);analytics.marketplaceBreakdown[marketplace]&&(analytics.marketplaceBreakdown[marketplace].units+=unitsValue,analytics.marketplaceBreakdown[marketplace].royalties+=royaltiesValue,analytics.marketplaceBreakdown[marketplace].returned+=returnedValue),analytics.marketplaceBreakdown.all.units+=unitsValue,analytics.marketplaceBreakdown.all.royalties+=royaltiesValue,analytics.marketplaceBreakdown.all.returned+=returnedValue}}),analytics}function extractRoyaltiesFromListing(listing){const royaltiesBadge=listing.querySelector(".royalties-badge span:last-child");if(royaltiesBadge){const royaltiesText=royaltiesBadge.textContent.trim(),royaltiesValue=parseRoyaltiesToUSD(royaltiesText);return isNaN(royaltiesValue)?0:royaltiesValue}return 0}function extractUnitsFromListing(listing){const unitsBadge=listing.querySelector(".order-units-badge span:last-child");if(unitsBadge){const unitsText=unitsBadge.textContent.trim();return parseInt(unitsText)||0}return 0}function extractReturnedFromListing(listing){const returnedBadge=listing.querySelector(".returned-units-badge span:last-child");if(returnedBadge){const returnedText=returnedBadge.textContent.trim();return parseInt(returnedText)||0}return 0}function extractCancelledFromListing(listing){const cancelledBadge=listing.querySelector(".canceled-units-badge span:last-child");if(cancelledBadge){const cancelledText=cancelledBadge.textContent.trim();return parseInt(cancelledText)||0}return 0}function extractNewFromListing(listing){return listing.querySelector(".new-seller-badge")?1:0}function extractAdFromListing(listing){return extractAdSalesFromListing(listing)}function extractAdSalesFromListing(listing){const adLabel=listing.querySelector(".listing-ad-row .listing-ad-label");if(adLabel){const salesMatch=adLabel.textContent.trim().match(/\((\d+)\)/);return salesMatch?parseInt(salesMatch[1]):0}const adSpendBadge=listing.querySelector(".ad-spend-badge span:last-child");if(adSpendBadge){const salesMatch=adSpendBadge.textContent.trim().match(/\((\d+)\)/);return salesMatch?parseInt(salesMatch[1]):0}return 0}function testAdSalesExtraction(){["$9.0 (6)","\xA33.0 (2)","\u20AC4.5 (3)","\u20AC6.0 (2)","\u20AC3.0 (2)","\u20AC0.0 (0)","\xA50.7 (0)","$0.0 (0)"].forEach(testCase=>{const salesMatch=testCase.match(/\((\d+)\)/),adSales=salesMatch?parseInt(salesMatch[1]):0})}function getListingMarketplace(listing){const flagImg=listing.querySelector(".listing-marketplace-flag");if(flagImg){const flagSrc=flagImg.src;if(flagSrc.includes("US.svg"))return"us";if(flagSrc.includes("UK.svg"))return"uk";if(flagSrc.includes("DE.svg"))return"de";if(flagSrc.includes("FR.svg"))return"fr";if(flagSrc.includes("IT.svg"))return"it";if(flagSrc.includes("ES.svg"))return"es";if(flagSrc.includes("JP.svg"))return"jp"}return"us"}function calculateMarketplaceSpecificAnalytics(salesCard,targetMarketplace){const allListings=salesCard.querySelectorAll(".listing-analytics-div"),analytics={totalRoyalties:0,totalUnits:0,totalReturned:0,totalCancelled:0,totalNew:0,totalAds:0,marketplaceBreakdown:{all:{units:0,royalties:0,returned:0},us:{units:0,royalties:0,returned:0},uk:{units:0,royalties:0,returned:0},de:{units:0,royalties:0,returned:0},fr:{units:0,royalties:0,returned:0},it:{units:0,royalties:0,returned:0},es:{units:0,royalties:0,returned:0},jp:{units:0,royalties:0,returned:0}}};return allListings.forEach(listing=>{if(window.getComputedStyle(listing).display!=="none"){const listingMarketplace=getListingMarketplace(listing);if(listingMarketplace===targetMarketplace){const royaltiesValue=extractRoyaltiesFromListing(listing),unitsValue=extractUnitsFromListing(listing),returnedValue=extractReturnedFromListing(listing);analytics.totalRoyalties+=royaltiesValue,analytics.totalUnits+=unitsValue,analytics.totalReturned+=returnedValue,analytics.totalCancelled+=extractCancelledFromListing(listing),analytics.totalNew+=extractNewFromListing(listing);const adSales=extractAdFromListing(listing);analytics.totalAds+=adSales,analytics.marketplaceBreakdown[listingMarketplace]&&(analytics.marketplaceBreakdown[listingMarketplace].units+=unitsValue,analytics.marketplaceBreakdown[listingMarketplace].royalties+=royaltiesValue,analytics.marketplaceBreakdown[listingMarketplace].returned+=returnedValue),analytics.marketplaceBreakdown.all.units+=unitsValue,analytics.marketplaceBreakdown.all.royalties+=royaltiesValue,analytics.marketplaceBreakdown.all.returned+=returnedValue}}}),analytics}function updateMainAnalyticsSection(salesCard,analytics){const analyticsDiv=salesCard.querySelector(".analytics-div");if(!analyticsDiv)return;const royaltiesValue=analyticsDiv.querySelector(".metric-value.royalties");if(royaltiesValue){const formattedRoyalties=analytics.totalRoyalties>=0?`$${analytics.totalRoyalties.toFixed(2)}`:`-$${Math.abs(analytics.totalRoyalties).toFixed(2)}`;royaltiesValue.textContent=formattedRoyalties,royaltiesValue.classList.remove("negative","zero"),analytics.totalRoyalties<0?royaltiesValue.classList.add("negative"):analytics.totalRoyalties===0&&royaltiesValue.classList.add("zero")}const returnedValue=analyticsDiv.querySelector(".metric-value.returned"),returnedPercentageElement=analyticsDiv.querySelector(".metric-percentage.returned-percentage");if(returnedValue)if(analytics.totalReturned===0||analytics.totalReturned===null||analytics.totalReturned===void 0)returnedValue.textContent="0",returnedValue.classList.add("zero"),returnedValue.classList.remove("negative"),returnedPercentageElement&&(returnedPercentageElement.style.display="none",returnedPercentageElement.textContent="0.0%");else if(isNaN(analytics.totalReturned))returnedValue.textContent="0",returnedValue.classList.add("zero"),returnedValue.classList.remove("negative"),returnedPercentageElement&&(returnedPercentageElement.style.display="none",returnedPercentageElement.textContent="0.0%");else if(returnedValue.textContent=formatReturnedMetric(analytics.totalReturned),returnedValue.classList.remove("zero"),returnedValue.classList.add("negative"),returnedPercentageElement&&analytics.totalUnits>0&&!isNaN(analytics.totalUnits)){const returnedPercentage=(Math.abs(analytics.totalReturned)/analytics.totalUnits*100).toFixed(1);returnedPercentageElement.textContent=`${returnedPercentage}%`,returnedPercentageElement.style.display="block"}else returnedPercentageElement&&(returnedPercentageElement.style.display="none",returnedPercentageElement.textContent="0.0%");const hasData=(analytics.totalUnits||0)>0;refreshCardNoDataUI(salesCard,hasData);const cancelledValue=analyticsDiv.querySelector(".metric-value.cancelled");cancelledValue&&(cancelledValue.textContent=analytics.totalCancelled.toString(),cancelledValue.classList.toggle("zero",analytics.totalCancelled===0),cancelledValue.classList.toggle("has-value",analytics.totalCancelled>0));const newValue=analyticsDiv.querySelector(".metric-value.new");newValue&&(newValue.textContent=analytics.totalNew.toString(),newValue.classList.toggle("zero",analytics.totalNew===0),newValue.classList.toggle("has-value",analytics.totalNew>0));const adsValue=analyticsDiv.querySelector(".metric-value.ads");adsValue&&(adsValue.textContent=analytics.totalAds.toString(),adsValue.classList.toggle("zero",analytics.totalAds===0),adsValue.classList.toggle("has-value",analytics.totalAds>0));const newPercentageElement=analyticsDiv.querySelector(".metric-percentage.new-percentage");if(newPercentageElement)if(analytics.totalNew===0||analytics.totalNew===null||analytics.totalNew===void 0)newPercentageElement.style.display="none",newPercentageElement.textContent="0.0%";else if(analytics.totalUnits>0&&!isNaN(analytics.totalNew)&&!isNaN(analytics.totalUnits)){const newPercentage=(analytics.totalNew/analytics.totalUnits*100).toFixed(1);newPercentageElement.textContent=`${newPercentage}%`,newPercentageElement.style.display="block"}else newPercentageElement.style.display="none",newPercentageElement.textContent="0.0%";const adsPercentageElement=analyticsDiv.querySelector(".metric-percentage.ads-percentage");if(adsPercentageElement)if(analytics.totalAds===0||analytics.totalAds===null||analytics.totalAds===void 0)adsPercentageElement.style.display="none",adsPercentageElement.textContent="0.0%";else if(analytics.totalUnits>0&&!isNaN(analytics.totalAds)&&!isNaN(analytics.totalUnits)){const adsPercentage=(analytics.totalAds/analytics.totalUnits*100).toFixed(1);adsPercentageElement.textContent=`${adsPercentage}%`,adsPercentageElement.style.display="block"}else adsPercentageElement.style.display="none",adsPercentageElement.textContent="0.0%";enforceZeroPercentageVisibility(salesCard);const hasDataMain=(analytics.totalUnits||0)>0;refreshCardNoDataUI(salesCard,hasDataMain)}function updateAllMarketplaceColumns(salesCard,analytics){Object.keys(analytics.marketplaceBreakdown).forEach(marketplace=>{const marketplaceData=analytics.marketplaceBreakdown[marketplace],marketplaceCol=salesCard.querySelector(`.marketplace-col.${marketplace==="all"?"all-marketplaces":marketplace}`);marketplaceCol&&marketplaceData&&updateMarketplaceColumn(marketplaceCol,marketplaceData,marketplace)})}function updateMarketplaceColumn(marketplaceCol,data,marketplace){const salesCountElement=marketplaceCol.querySelector(".marketplace-total-sales-count");salesCountElement&&(salesCountElement.textContent=data.units.toString(),salesCountElement.classList.toggle("zero",data.units===0));const royaltiesElement=marketplaceCol.querySelector(".marketplace-total-earned-royalties");if(royaltiesElement){const formattedRoyalties=formatRoyaltiesForMarketplace(data.royalties,marketplace);royaltiesElement.textContent=formattedRoyalties,royaltiesElement.classList.remove("zero","negative"),data.royalties===0?royaltiesElement.classList.add("zero"):data.royalties<0&&royaltiesElement.classList.add("negative")}const returnedElement=marketplaceCol.querySelector(".marketplace-total-returned-units");if(returnedElement){let formattedReturned;formattedReturned=formatReturnedUnits(data.returned),returnedElement.textContent=formattedReturned,returnedElement.classList.remove("zero","negative"),data.returned===0?returnedElement.classList.add("zero"):data.returned<0&&returnedElement.classList.add("negative")}}function formatRoyaltiesForMarketplace(amount,marketplace){if(amount===0)return{us:"$0.00",all:"$0.00",uk:"\xA30.00",de:"\u20AC0.00",fr:"\u20AC0.00",it:"\u20AC0.00",es:"\u20AC0.00",jp:"\xA50"}[marketplace]||"$0.00";const formattedAmount=Math.abs(amount).toFixed(2),sign=amount<0?"-":"";switch(marketplace){case"us":case"all":return`${sign}$${formattedAmount}`;case"uk":return`${sign}\xA3${formattedAmount}`;case"de":case"fr":case"it":case"es":return`${sign}\u20AC${formattedAmount}`;case"jp":return`${sign}\xA5${Math.round(amount*100).toLocaleString()}`;default:return`${sign}$${formattedAmount}`}}function restoreOriginalAnalyticsValues(salesCard){}(function(){const style=document.createElement("style");style.textContent=`
    .snap-dropdown.focused .dropdown-header {
      border-width: 1.5px !important;
      border-color: #470CED !important;
    }
  `,document.head.appendChild(style)})();const adSpendTimeframes=["Today's Ad Spend","Yesterday's Ad Spend","This Week's Ad Spend","Last Week's Ad Spend","Last 7 Days' Ad Spend","Last 30 Days' Ad Spend","This Month's Ad Spend","Last Month's Ad Spend","Year to Date Ad Spend","Lifetime Ad Spend"],adSpendMockData=[{spend:"$12,237.92",orders:"66",marketplaces:[{currency:"$403,899",orders:"13",acos:"9.956",flag:"./assets/US.svg",acosIcon:"./assets/ACOS-low-ic.svg"},{currency:"\xA33,899",orders:"13",acos:"9.956",flag:"./assets/UK.svg",acosIcon:"./assets/ACOS-low-ic.svg"},{currency:"\u20AC32.89",orders:"22",acos:"9.956",flag:"./assets/DE.svg",acosIcon:"./assets/ACOS-low-ic.svg"},{currency:"\u20AC3.99",orders:"1",acos:"30.956",flag:"./assets/FR.svg",acosIcon:"./assets/ACOS-high-ic.svg"},{currency:"\u20AC11.89",orders:"0",acos:"",flag:"./assets/IT.svg",acosIcon:""},{currency:"\u20AC3.99",orders:"7",acos:"7.956",flag:"./assets/ES.svg",acosIcon:"./assets/ACOS-low-ic.svg"},{currency:"\xA53.99",orders:"0",acos:"",flag:"./assets/JP.svg",acosIcon:""}]},{spend:"$10,000.00",orders:"55",marketplaces:[{currency:"$350,000",orders:"10",acos:"8.5",flag:"./assets/US.svg",acosIcon:"./assets/ACOS-low-ic.svg"},{currency:"\xA33,000",orders:"10",acos:"8.5",flag:"./assets/UK.svg",acosIcon:"./assets/ACOS-low-ic.svg"},{currency:"\u20AC30.00",orders:"20",acos:"8.5",flag:"./assets/DE.svg",acosIcon:"./assets/ACOS-low-ic.svg"},{currency:"\u20AC2.99",orders:"1",acos:"25.0",flag:"./assets/FR.svg",acosIcon:"./assets/ACOS-high-ic.svg"},{currency:"\u20AC10.00",orders:"0",acos:"",flag:"./assets/IT.svg",acosIcon:""},{currency:"\u20AC2.99",orders:"5",acos:"6.0",flag:"./assets/ES.svg",acosIcon:"./assets/ACOS-low-ic.svg"},{currency:"\xA52.99",orders:"0",acos:"",flag:"./assets/JP.svg",acosIcon:""}]}];for(;adSpendMockData.length<adSpendTimeframes.length;)adSpendMockData.push(adSpendMockData[0]);function initializeCustomTooltips(){if(document.querySelectorAll(".listing-badge.fit-types-badge").forEach(badge=>{document.querySelectorAll(".fit-type-tooltip").forEach(tooltip2=>{(tooltip2.dataset.parentBadge===badge.id||!tooltip2.dataset.parentBadge)&&tooltip2.remove()});const men=badge.querySelector(".male-type span")?.textContent||"0",women=badge.querySelector(".female-type span")?.textContent||"0",unisex=badge.querySelector(".unisex-type span")?.textContent||"0",youth=badge.querySelector(".youth-type span")?.textContent||"0",girls=badge.querySelector(".girls-type span")?.textContent||"0";badge.removeAttribute("data-tooltip");const tooltipRows=[{label:"Men",value:men},{label:"Women",value:women},{label:"Unisex",value:unisex},{label:"Youth",value:youth},{label:"Girls",value:girls}].filter(fitType=>(parseInt(fitType.value)||0)>0).map(fitType=>`<div class="fit-type-tooltip-row">${fitType.label}:&nbsp;<span>${fitType.value}</span></div>`).join(""),tooltip=document.createElement("div");tooltip.className="fit-type-tooltip",tooltip.dataset.parentBadge=badge.id||`fit-type-${Date.now()}-${Math.random()}`,tooltip.innerHTML=`
      <div class="fit-type-tooltip-title">Fit Type</div>
      ${tooltipRows}
      <div class="fit-type-tooltip-arrow"></div>
    `,tooltip.style.display="none",document.body.appendChild(tooltip);function showTooltip(){const rect=badge.getBoundingClientRect();tooltip.style.position="fixed",tooltip.style.left=rect.left+rect.width/2+"px",tooltip.style.bottom=window.innerHeight-rect.top+4+"px",tooltip.style.transform="translateX(-50%)",tooltip.style.display="block",tooltip.style.zIndex="10000000",activeCustomTooltips.set(badge,{isVisible:!0,hideFunction:hideTooltip,showFunction:showTooltip})}function hideTooltip(){tooltip.style.display="none",activeCustomTooltips.delete(badge)}window.EventCleanupManager.addEventListener(badge,"mouseenter",showTooltip),window.EventCleanupManager.addEventListener(badge,"mouseleave",hideTooltip),window.EventCleanupManager.addEventListener(badge,"focus",showTooltip),window.EventCleanupManager.addEventListener(badge,"blur",hideTooltip)}),document.querySelectorAll(".listing-badge.ordered-colors-badge").forEach(badge=>{document.querySelectorAll(".ordered-colors-tooltip").forEach(tooltip2=>{(tooltip2.dataset.parentBadge===badge.id||!tooltip2.dataset.parentBadge)&&tooltip2.remove()});const colorItems=badge.querySelectorAll(".color-item");let tooltipRows="";colorItems.forEach(item=>{const colorCircle=item.querySelector(".color-circle"),colorNumber=item.querySelector(".color-number");if(colorCircle&&colorNumber){const bgColor=colorCircle.style.backgroundColor,number=colorNumber.textContent;let colorName="Unknown";bgColor.includes("0, 0, 0")||bgColor==="#000000"||bgColor==="rgb(0, 0, 0)"?colorName="Black":bgColor.includes("255, 255, 255")||bgColor==="#FFFFFF"||bgColor==="rgb(255, 255, 255)"?colorName="White":bgColor.includes("255, 0, 0")||bgColor==="#FF0000"||bgColor==="rgb(255, 0, 0)"?colorName="Red":bgColor.includes("0, 102, 204")||bgColor==="#0066CC"||bgColor==="rgb(0, 102, 204)"?colorName="Blue":bgColor.includes("0, 170, 0")||bgColor==="#00AA00"||bgColor==="rgb(0, 170, 0)"?colorName="Green":bgColor.includes("255, 107, 53")||bgColor==="#FF6B35"||bgColor==="rgb(255, 107, 53)"?colorName="Orange":bgColor.includes("139, 69, 19")||bgColor==="#8B4513"||bgColor==="rgb(139, 69, 19)"?colorName="Brown":(bgColor.includes("255, 149, 0")||bgColor==="#FF9500"||bgColor==="rgb(255, 149, 0)")&&(colorName="Orange");const colorCircleStyle=`
          width: 8px;
          height: 8px;
          border-radius: 50%;
          display: inline-block;
          background-color: ${bgColor};
          border: 0.5px solid var(--border-color);
          margin-right: 6px;
          vertical-align: baseline;
          position: relative;
          top: -2px;
          flex-shrink: 0;
        `;tooltipRows+=`<div class="ordered-colors-tooltip-row">
          <span style="${colorCircleStyle}"></span>${colorName}:&nbsp;<span>${number}</span>
        </div>`}}),badge.removeAttribute("data-tooltip");const tooltip=document.createElement("div");tooltip.className="ordered-colors-tooltip",tooltip.dataset.parentBadge=badge.id||`ordered-colors-${Date.now()}-${Math.random()}`,tooltip.innerHTML=`
      <div class="ordered-colors-tooltip-title">Sold Colors</div>
      ${tooltipRows}
      <div class="ordered-colors-tooltip-arrow"></div>
    `,tooltip.style.display="none",document.body.appendChild(tooltip);function showTooltip(){const rect=badge.getBoundingClientRect();tooltip.style.position="fixed",tooltip.style.left=rect.left+rect.width/2+"px",tooltip.style.bottom=window.innerHeight-rect.top+4+"px",tooltip.style.transform="translateX(-50%)",tooltip.style.display="block",tooltip.style.zIndex="10000000",activeCustomTooltips.set(badge,{isVisible:!0,hideFunction:hideTooltip,showFunction:showTooltip})}function hideTooltip(){tooltip.style.display="none",activeCustomTooltips.delete(badge)}window.EventCleanupManager.addEventListener(badge,"mouseenter",showTooltip),window.EventCleanupManager.addEventListener(badge,"mouseleave",hideTooltip),window.EventCleanupManager.addEventListener(badge,"focus",showTooltip),window.EventCleanupManager.addEventListener(badge,"blur",hideTooltip)}),document.querySelectorAll(".listing-ad-row").forEach(adRow=>{document.querySelectorAll(".ad-spend-tooltip").forEach(tooltip2=>{(tooltip2.dataset.parentRow===adRow.id||!tooltip2.dataset.parentRow)&&tooltip2.remove()}),adRow.removeAttribute("data-tooltip");let impressions="",clicks="",cpc="";const tooltipText=adRow.getAttribute("data-tooltip")||"",match=tooltipText.match(/Impressions:\s*([\d,]+)/i);match&&(impressions=match[1]);const match2=tooltipText.match(/Clicks:\s*(\d+)/i);match2&&(clicks=match2[1]);const match3=tooltipText.match(/CPC:\s*([$€£¥]?[\d.]+)/i);match3&&(cpc=match3[1]),impressions||(impressions="2343"),clicks||(clicks="12"),cpc||(cpc="$0.21");let cps="N/A";try{const cpcValue=parseFloat(cpc.replace(/[$€£¥,]/g,"")),clicksValue=parseInt(clicks.replace(/,/g,"")),currencySymbol=cpc.match(/^[$€£¥]/)?.[0]||"$",estimatedSales=Math.max(1,Math.round(clicksValue*.08)),cpsValue=cpcValue*clicksValue/estimatedSales;cps=`${currencySymbol}${cpsValue.toFixed(2)}`}catch{cps="$2.63"}const adLabel=adRow.querySelector(".listing-ad-label");let statusLabel="EFFICIENT",statusClass="efficient";if(adLabel){const adSpendText=adLabel.textContent.trim();switch(getAdSpendStatus(0,adSpendText).status){case"high":statusLabel="OVERSPENT",statusClass="overspent";break;case"medium":statusLabel="HIGH",statusClass="high";break;case"low":statusLabel="EFFICIENT",statusClass="efficient";break;case"no-sales":statusLabel="NO SALES",statusClass="no-sales";break;default:statusLabel="EFFICIENT",statusClass="efficient"}}const tooltip=document.createElement("div");tooltip.className="ad-spend-tooltip",tooltip.dataset.parentRow=adRow.id||`ad-spend-${Date.now()}-${Math.random()}`,tooltip.innerHTML=`
      <div class="ad-spend-tooltip-title">Ad Spend <span class="ad-spend-status-label ${statusClass}">${statusLabel}</span></div>
      <div class="ad-spend-tooltip-row">Impressions:&nbsp;<span>${impressions}</span></div>
      <div class="ad-spend-tooltip-row">Clicks:&nbsp;<span>${clicks}</span></div>
      <div class="ad-spend-tooltip-row">CPC:&nbsp;<span>${cpc}</span></div>
      <div class="ad-spend-tooltip-row">CPS:&nbsp;<span>${cps}</span></div>
      <div class="ad-spend-tooltip-arrow"></div>
    `,tooltip.style.display="none",document.body.appendChild(tooltip);function showTooltip(){const rect=adRow.getBoundingClientRect();tooltip.style.position="fixed",tooltip.style.left=rect.left+rect.width/2+"px",tooltip.style.bottom=window.innerHeight-rect.top+4+"px",tooltip.style.transform="translateX(-50%)",tooltip.style.display="block",tooltip.style.zIndex="10000000",activeCustomTooltips.set(adRow,{isVisible:!0,hideFunction:hideTooltip,showFunction:showTooltip})}function hideTooltip(){tooltip.style.display="none",activeCustomTooltips.delete(adRow)}window.EventCleanupManager.addEventListener(adRow,"mouseenter",showTooltip),window.EventCleanupManager.addEventListener(adRow,"mouseleave",hideTooltip),window.EventCleanupManager.addEventListener(adRow,"focus",showTooltip),window.EventCleanupManager.addEventListener(adRow,"blur",hideTooltip)}),!document.getElementById("fit-type-tooltip-style")){const style=document.createElement("style");style.id="fit-type-tooltip-style",style.textContent=`
      .fit-type-tooltip, .ad-spend-tooltip, .ordered-colors-tooltip {
        position: fixed !important;
        /* Do not set left/bottom in CSS - let JavaScript handle positioning */
        background: #000000 !important;
        color: #FFFFFF !important;
        border-radius: 6px;
        padding: 12px 16px 8px 16px;
        font-family: 'Amazon Ember', Arial, sans-serif;
        font-size: var(--tooltip-font-size, 12px);
        font-weight: 500;
        z-index: 10000000 !important;
        min-width: 120px;
        text-align: left;
        line-height: 1.4;
        pointer-events: none;
        display: none;
        white-space: normal;
      }
      .ad-spend-status-label {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 2px 8px;
        border-radius: 2px;
        font-family: 'Amazon Ember', Arial, sans-serif;
        font-size: 10px;
        font-weight: 500;
        margin-left: 8px;
        height: 12px;
        min-height: 12px;
        max-height: 12px;
        line-height: 1;
        text-align: center;
        width: fit-content;
        border: none;
        vertical-align: middle;
      }
      .ad-spend-status-label.overspent {
        background: rgba(255, 57, 31, 0.1);
        color: #FF391F;
      }
      .ad-spend-status-label.high {
        background: rgba(253, 195, 0, 0.1);
        color: #FDC300;
      }
      .ad-spend-status-label.efficient {
        background: rgba(4, 174, 44, 0.1);
        color: #04AE2C;
      }
      .ad-spend-status-label.no-sales {
        background: rgba(96, 111, 149, 0.1);
        color: #606F95;
      }
      .fit-type-tooltip-title, .ad-spend-tooltip-title, .ordered-colors-tooltip-title {
        font-weight: 700;
        font-size: var(--tooltip-font-size, 12px);
        margin-bottom: 8px;
        color: var(--tooltip-text, #FFFFFF);
        line-height: 1.4;
      }
      .fit-type-tooltip-row, .ad-spend-tooltip-row, .ordered-colors-tooltip-row {
        font-size: var(--tooltip-font-size, 12px);
        font-weight: 500;
        margin-bottom: 4px;
        display: flex;
        align-items: center;
        color: var(--tooltip-text, #FFFFFF);
        line-height: 1.4;
      }
      .fit-type-tooltip-row:last-child, .ad-spend-tooltip-row:last-child, .ordered-colors-tooltip-row:last-child {
        margin-bottom: 0;
      }
      .fit-type-tooltip-arrow, .ad-spend-tooltip-arrow, .ordered-colors-tooltip-arrow {
        position: absolute;
        bottom: -5px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid #000000;
      }
      .listing-badge.fit-types-badge, .listing-ad-row, .listing-badge.ordered-colors-badge {
        position: relative;
      }
      
      /* Dark theme adjustments */
      [data-theme="dark"] .fit-type-tooltip, 
      [data-theme="dark"] .ad-spend-tooltip, 
      [data-theme="dark"] .ordered-colors-tooltip {
        background: #000000 !important;
        color: #FFFFFF !important;
      }
      
      [data-theme="dark"] .fit-type-tooltip-arrow, 
      [data-theme="dark"] .ad-spend-tooltip-arrow, 
      [data-theme="dark"] .ordered-colors-tooltip-arrow {
        border-top-color: #000000 !important;
      }
      
      [data-theme="dark"] .fit-type-tooltip-title, 
      [data-theme="dark"] .ad-spend-tooltip-title, 
      [data-theme="dark"] .ordered-colors-tooltip-title,
      [data-theme="dark"] .fit-type-tooltip-row, 
      [data-theme="dark"] .ad-spend-tooltip-row, 
      [data-theme="dark"] .ordered-colors-tooltip-row {
        color: #FFFFFF !important;
      }
    `,document.head.appendChild(style)}}window.initializeCustomTooltips=initializeCustomTooltips,setTimeout(()=>{initializeCustomTooltips()},0);function initMarketplaceToggle(){const salesCardStates2={0:{selectedMarketplace:"all"},1:{selectedMarketplace:"all"}},salesCards=document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div");if(salesCards.length===0)return;salesCards.forEach((salesCard,cardIndex)=>{const marketplaceCols=salesCard.querySelectorAll(".marketplace-col");marketplaceCols.length!==0&&(marketplaceCols.forEach(col=>{col.classList.add("active")}),marketplaceCols.forEach(col=>{window.EventCleanupManager.addEventListener(col,"click",function(){const marketplaceId=getMarketplaceId(col);selectMarketplace(marketplaceId,cardIndex)}),col.setAttribute("tabindex","0"),window.EventCleanupManager.addEventListener(col,"keydown",e=>{if(e.key==="Enter"||e.key===" "){e.preventDefault();const marketplaceId=getMarketplaceId(col);selectMarketplace(marketplaceId,cardIndex)}})}))});function getMarketplaceId(col){return col.classList.contains("all-marketplaces")?"all":col.classList.contains("us")?"us":col.classList.contains("uk")?"uk":col.classList.contains("de")?"de":col.classList.contains("fr")?"fr":col.classList.contains("it")?"it":col.classList.contains("es")?"es":col.classList.contains("jp")?"jp":"all"}function selectMarketplace(marketplaceId,cardIndex){salesCardStates2[cardIndex].selectedMarketplace=marketplaceId,clearSearchStateForCard(cardIndex),updateMarketplaceVisualStates(cardIndex),updateAllMarketplacesIcon(cardIndex),filterSalesData(marketplaceId,cardIndex);const salesCard=document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div")[cardIndex];salesCard&&setTimeout(()=>{updateNewTabCount(salesCard)},30)}function clearSearchStateForCard(cardIndex){const salesCard=document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div")[cardIndex];if(!salesCard)return;const searchInput=salesCard.querySelector(".search-input"),closeSearchIcon=salesCard.querySelector(".close-search-icon"),searchNoResults=salesCard.querySelector(".search-no-results");searchInput&&searchNoResults&&(searchInput.value="",closeSearchIcon&&(closeSearchIcon.style.opacity="0",closeSearchIcon.style.pointerEvents="none"),searchNoResults.style.display="none")}function updateMarketplaceVisualStates(cardIndex){const salesCard=document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div")[cardIndex],marketplaceCols=salesCard.querySelectorAll(".marketplace-col"),selectedMarketplace=salesCardStates2[cardIndex].selectedMarketplace,marketplacesDiv=salesCard.querySelector(".marketplaces-div");marketplacesDiv&&marketplacesDiv.classList.remove("single-marketplace-active"),marketplaceCols.forEach(col=>{const colMarketplaceId=getMarketplaceId(col);selectedMarketplace==="all"||colMarketplaceId===selectedMarketplace?(col.classList.remove("inactive"),col.classList.add("active")):(col.classList.remove("active"),col.classList.add("inactive"))}),selectedMarketplace!=="all"&&marketplacesDiv&&marketplacesDiv.classList.add("single-marketplace-active")}function updateAllMarketplacesIcon(cardIndex){const allMarketplacesIcon=document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div")[cardIndex].querySelector(".all-marketplaces img"),selectedMarketplace=salesCardStates2[cardIndex].selectedMarketplace;allMarketplacesIcon&&(selectedMarketplace==="all"?(allMarketplacesIcon.src="./assets/all-marketplaces-active-ic.svg",allMarketplacesIcon.alt="All Marketplaces Active"):(allMarketplacesIcon.src="./assets/all-marketplaces-inactive-ic.svg",allMarketplacesIcon.alt="All Marketplaces Inactive"))}function filterSalesData(marketplace,cardIndex){const salesCard=document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div")[cardIndex];if(updateListingVisibility2(salesCard,marketplace),marketplace==="all"){const analytics=calculateComprehensiveAnalytics(salesCard),salesCount=salesCard.querySelector(".sales-count");salesCount&&(salesCount.textContent=analytics.totalUnits.toString(),salesCount.classList.toggle("zero",analytics.totalUnits===0)),updateAllPercentageCalculations(salesCard,"all"),updateNoSalesStateForCard(salesCard,analytics.totalUnits)}else{const marketplaceAnalytics=calculateMarketplaceSpecificAnalytics(salesCard,marketplace),salesCount=salesCard.querySelector(".sales-count");salesCount&&(salesCount.textContent=marketplaceAnalytics.totalUnits.toString(),salesCount.classList.toggle("zero",marketplaceAnalytics.totalUnits===0)),updateAllPercentageCalculations(salesCard,marketplace),updateNoSalesStateForCard(salesCard,marketplaceAnalytics.totalUnits),updateSalesAnalyticsForMarketplace(salesCard,marketplace,0)}}function getSalesDataForMarketplace(marketplace,cardIndex){return null}function updateAnalyticsMetrics(salesCard,salesData){}function updateListingVisibility2(salesCard,marketplace){const listingDivs=salesCard.querySelectorAll(".listing-analytics-div"),noSalesState=salesCard.querySelector(".no-sales-state"),listingDividers=salesCard.querySelectorAll(".listing-section-divider"),searchTabsDiv=salesCard.querySelector(".search-tabs-div"),searchNoResults=salesCard.querySelector(".search-no-results"),searchInput=salesCard.querySelector(".search-input"),closeSearchIcon=salesCard.querySelector(".close-search-icon"),salesFilterDiv=salesCard.querySelector(".sales-filter-div"),searchDiv=salesCard.querySelector(".search-div");noSalesState&&(noSalesState.style.display="none"),searchNoResults&&(searchNoResults.style.display="none"),searchInput&&(searchInput.value=""),closeSearchIcon&&(closeSearchIcon.style.opacity="0",closeSearchIcon.style.pointerEvents="none"),searchTabsDiv&&(searchTabsDiv.style.display="block"),salesFilterDiv&&(salesFilterDiv.style.display="flex"),searchDiv&&(searchDiv.style.display=""),listingDivs.forEach(listingDiv=>{const marketplaceFlag=listingDiv.querySelector(".listing-marketplace-flag");if(marketplace==="all"){listingDiv.style.display="flex";const nextDivider=listingDiv.nextElementSibling;nextDivider&&nextDivider.classList.contains("listing-section-divider")&&(nextDivider.style.display="")}else if(marketplaceFlag){const flagSrc=marketplaceFlag.src,shouldShow=checkIfListingMatchesMarketplace2(flagSrc,marketplace);listingDiv.style.display=shouldShow?"flex":"none";const nextDivider=listingDiv.nextElementSibling;nextDivider&&nextDivider.classList.contains("listing-section-divider")&&(nextDivider.style.display=shouldShow?"":"none")}});const visibleListings=Array.from(listingDivs).filter(listing=>window.getComputedStyle(listing).display!=="none");let totalActualSales=0;visibleListings.forEach(listing=>{const unitsBadge=listing.querySelector(".order-units-badge span:last-child");if(unitsBadge){const unitsText=unitsBadge.textContent.trim(),unitsValue=parseInt(unitsText)||0;totalActualSales+=unitsValue}}),ensureListingDividers(salesCard);const currentVisibleListings=Array.from(listingDivs).filter(listing=>window.getComputedStyle(listing).display!=="none");if(currentVisibleListings.length>0){const lastDivider=currentVisibleListings[currentVisibleListings.length-1].nextElementSibling;lastDivider&&lastDivider.classList.contains("listing-section-divider")&&(lastDivider.style.display="none")}updateNewTabCount(salesCard);const activeTab=salesCard.querySelector(".sales-filter-tab.active");activeTab&&activeTab.classList.contains("new-tab")&&setTimeout(()=>{if(countNewSellerBadges(salesCard)===0){const cardIndex=Array.from(document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div")).indexOf(salesCard);let defaultTab;cardIndex===0?defaultTab=salesCard.querySelector(".time-tab"):defaultTab=salesCard.querySelector(".units-tab"),defaultTab&&(updateTabStates(salesCard,defaultTab,"desc"),sortListings(salesCard,getSortTypeFromTab(defaultTab),"desc"))}},20)}function updateDividerVisibility(salesCard){const listingDivs=salesCard.querySelectorAll(".listing-analytics-div"),listingDividers=salesCard.querySelectorAll(".listing-section-divider"),visibleListings=Array.from(listingDivs).filter(div=>window.getComputedStyle(div).display!=="none");if(listingDividers.forEach(divider=>divider.style.display="none"),visibleListings.length>=2){const allElements=Array.from(salesCard.children).filter(el=>el.classList.contains("listing-analytics-div")||el.classList.contains("listing-section-divider"));let lastVisibleListingElement=null;for(const element of allElements)element.classList.contains("listing-analytics-div")?window.getComputedStyle(element).display!=="none"&&(lastVisibleListingElement=element):element.classList.contains("listing-section-divider")&&lastVisibleListingElement&&allElements.slice(allElements.indexOf(element)+1).find(el=>el.classList.contains("listing-analytics-div")&&window.getComputedStyle(el).display!=="none")&&(element.style.display="block")}}function checkIfListingMatchesMarketplace2(flagSrc,marketplace){const marketplaceFlags={us:"US.svg",uk:"UK.svg",de:"DE.svg",fr:"FR.svg",it:"IT.svg",es:"ES.svg",jp:"JP.svg"};return flagSrc.includes(marketplaceFlags[marketplace])}function initAnalyticsMetrics(){document.querySelectorAll(".metric-value").forEach(metric=>{const text=metric.textContent.trim();if((text==="0"||text==="$0"||text==="\u20AC0"||text==="\xA30"||text==="\xA50"||text==="0%"||text==="(0)"||text.includes("0.0"))&&metric.classList.add("zero"),metric.classList.contains("royalties")&&(text.includes("-")||text.startsWith("-"))&&metric.classList.add("negative"),metric.classList.contains("returned")){const currentText=metric.textContent.trim();if(currentText.includes("(")&&currentText.includes(")")&&!currentText.includes("%")){const match=currentText.match(/\((-?\d+)\)/);if(match){const returnCount=parseInt(match[1]),percentage=Math.abs(returnCount*2.7).toFixed(1);metric.textContent=`${currentText} ${percentage}%`}}}})}document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div").forEach((salesCard,cardIndex)=>{ensureListingDividers(salesCard)})}function runDelayedDividerFix(){setTimeout(()=>{document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div").forEach((salesCard,cardIndex)=>{ensureListingDividers(salesCard)})},1e3)}runDelayedDividerFix();function getAdSpendStatus(salesCount,adSpendText){const currencyMatch=adSpendText.match(/([€£¥$])([\d.]+)/),currency=currencyMatch?currencyMatch[1]:"$",spendAmount=currencyMatch?parseFloat(currencyMatch[2]):0,salesMatch=adSpendText.match(/\((\d+)\)/),actualSales=salesMatch?parseInt(salesMatch[1]):0;if(actualSales===0)return spendAmount<1?{icon:"./assets/ad-no-sales-ic.svg",color:"#606F95",status:"no-sales"}:spendAmount>=1&&spendAmount<2.5?{icon:"./assets/ad-medium-ic.svg",color:"#FDC300",status:"medium"}:{icon:"./assets/ad-high-ic.svg",color:"#FF391F",status:"high"};const costPerSale=spendAmount/actualSales;return costPerSale<=1.5?{icon:"./assets/ad-low-ic.svg",color:"#04AE2C",status:"low"}:costPerSale<=2.5?{icon:"./assets/ad-medium-ic.svg",color:"#FDC300",status:"medium"}:{icon:"./assets/ad-high-ic.svg",color:"#FF391F",status:"high"}}function initializeAdSpendStatus(){const adRows=document.querySelectorAll(".listing-ad-row");adRows.length!==0&&(adRows.forEach((adRow,index)=>{const adLabel=adRow.querySelector(".listing-ad-label"),adIcon=adRow.querySelector(".listing-ad-ic");if(adLabel&&adIcon){const adSpendText=adLabel.textContent.trim(),status=getAdSpendStatus(0,adSpendText);adIcon.src=status.icon,adIcon.alt=`Ad Spend ${status.status}`,adLabel.style.color=status.color,adLabel.style.fontWeight="700",adRow.classList.remove("ad-no-sales","ad-low","ad-medium","ad-high","ad-default"),adRow.classList.add(`ad-${status.status}`)}}),hideAdRowsWithZeroValues())}function initializeSortingTabs(){document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div").forEach((salesCard,cardIndex)=>{initializeCardSorting(salesCard,cardIndex)})}function initializeCardSorting(salesCard,cardIndex){const sortTabs=salesCard.querySelectorAll(".sales-filter-tab");let currentSortType=cardIndex===0?"time":"units",currentSortDirection="desc";sortTabs.forEach(tab=>{window.EventCleanupManager.addEventListener(tab,"click",()=>{const sortType=getSortTypeFromTab(tab);if(sortType==="new"){const listings=Array.from(salesCard.querySelectorAll(".listing-analytics-div"));let hasNewSeller=!1;if(listings.filter(listing=>window.getComputedStyle(listing).display!=="none").forEach((listing,idx)=>{const titleRow=listing.querySelector(".listing-title-row"),found=titleRow?titleRow.querySelector(".new-seller-badge")!==null:!1,foundAnywhere=listing.querySelector(".new-seller-badge")!==null;(found||foundAnywhere)&&(hasNewSeller=!0)}),!hasNewSeller)return}currentSortType===sortType?currentSortDirection=currentSortDirection==="asc"?"desc":"asc":(currentSortDirection=sortType==="time"||sortType==="units"||sortType==="royalties"||sortType==="ad-spend"?"desc":"asc",currentSortType=sortType),updateTabStates(salesCard,tab,currentSortDirection),sortListings(salesCard,sortType,currentSortDirection)})}),cardIndex===0?salesCard.querySelector(".time-tab")&&sortListings(salesCard,"time","desc"):salesCard.querySelector(".units-tab")&&sortListings(salesCard,"units","desc")}function getSortTypeFromTab(tab){return tab.classList.contains("time-tab")?"time":tab.classList.contains("units-tab")?"units":tab.classList.contains("royalties-tab")?"royalties":tab.classList.contains("new-tab")?"new":tab.classList.contains("ad-spend-tab")?"ad-spend":"time"}function updateTabStates(salesCard,activeTab,direction){const allTabs=salesCard.querySelectorAll(".sales-filter-tab"),allSortIcons=salesCard.querySelectorAll(".tab-sort-icon");allTabs.forEach(tab=>tab.classList.remove("active")),allSortIcons.forEach(icon=>icon.classList.remove("active")),activeTab.classList.add("active");const sortIcon=activeTab.querySelector(".tab-sort-icon");sortIcon&&(sortIcon.classList.add("active"),sortIcon.src=direction==="asc"?"./assets/Ascending.svg":"./assets/Descending.svg",sortIcon.alt=direction==="asc"?"Ascending":"Descending")}function sortListings(salesCard,sortType,direction){const listingsContainer=salesCard,listings=Array.from(salesCard.querySelectorAll(".listing-analytics-div")),dividers=Array.from(salesCard.querySelectorAll(".listing-section-divider"));if(listings.length===0)return;const sortableListings=listings.map((listing,index)=>{const sortValue=extractSortValue(listing,sortType);let associatedDivider=null,nextElement=listing.nextElementSibling;for(;nextElement;){if(nextElement.classList.contains("listing-section-divider")){associatedDivider=nextElement;break}else if(nextElement.classList.contains("listing-analytics-div"))break;nextElement=nextElement.nextElementSibling}return{element:listing,divider:associatedDivider,sortValue,originalIndex:index}}),validListings=sortableListings.filter(item=>item.sortValue!==null);validListings.length!==0&&(validListings.sort((a,b)=>{const comparison=compareValues(a.sortValue,b.sortValue,sortType);return direction==="asc"?comparison:-comparison}),reorderListingsInDOM(salesCard,validListings,sortableListings))}function extractSortValue(listing,sortType){switch(sortType){case"time":return extractTimeValue(listing);case"units":return extractUnitsValue(listing);case"royalties":return extractRoyaltiesValue(listing);case"new":return extractNewSellerValue(listing);case"ad-spend":return extractAdSpendValue(listing);default:return null}}function extractTimeValue(listing){const timeSpan=listing.querySelector(".sold-time-badge span");if(!timeSpan)return null;const timeText=timeSpan.textContent.trim();return parseTimeToMinutes(timeText)}function extractUnitsValue(listing){const unitsSpan=listing.querySelector(".order-units-badge span");if(!unitsSpan)return null;const match=unitsSpan.textContent.trim().match(/\+(\d+)/);return match?parseInt(match[1]):null}function extractRoyaltiesValue(listing){const royaltiesSpan=listing.querySelector(".royalties-badge span");if(!royaltiesSpan)return null;const royaltiesText=royaltiesSpan.textContent.trim();return parseRoyaltiesToUSD(royaltiesText)}function extractNewSellerValue(listing){const titleRow=listing.querySelector(".listing-title-row"),foundInTitleRow=titleRow?titleRow.querySelector(".new-seller-badge")!==null:!1,foundAnywhere=listing.querySelector(".new-seller-badge")!==null;return foundInTitleRow||foundAnywhere?1:0}function extractAdSpendValue(listing){const adSpendLabel=listing.querySelector(".listing-ad-label");if(!adSpendLabel)return null;const match=adSpendLabel.textContent.trim().match(/[\$£€¥]?([\d.]+)/);return match?parseFloat(match[1]):null}function parseTimeToMinutes(timeText){const match=timeText.match(/(\d{1,2}):(\d{2})\s*(AM|PM)/i);if(!match)return null;let hours=parseInt(match[1]);const minutes=parseInt(match[2]),period=match[3].toUpperCase();return period==="PM"&&hours!==12?hours+=12:period==="AM"&&hours===12&&(hours=0),hours*60+minutes}function parseRoyaltiesToUSD(royaltiesText){const match=royaltiesText.match(/([\d.]+)/);if(!match)return null;const amount=parseFloat(match[1]),conversionRates={$:1,"\xA3":1.27,"\u20AC":1.08,"\xA5":.0067},currency=royaltiesText.charAt(0),rate=conversionRates[currency]||1;return amount*rate}function compareValues(a,b,sortType){return a===null&&b===null?0:a===null?1:b===null?-1:sortType==="new"?b-a:a-b}function reorderListingsInDOM(salesCard,sortedListings,allListings){const searchTabsDiv=salesCard.querySelector(".search-tabs-div"),noResultsDiv=salesCard.querySelector(".search-no-results"),noSalesDiv=salesCard.querySelector(".no-sales-state");let insertionPoint=searchTabsDiv;noResultsDiv&&noResultsDiv.nextElementSibling&&(insertionPoint=noSalesDiv||noResultsDiv);const fragment=document.createDocumentFragment(),elementsToRemove=[];allListings.forEach(item=>{item.element.parentNode&&elementsToRemove.push(item.element),item.divider&&item.divider.parentNode&&elementsToRemove.push(item.divider)}),elementsToRemove.forEach(element=>{element.parentNode.removeChild(element)});const allItemsToRender=[];sortedListings.forEach((item,index)=>{if(allItemsToRender.push(item.element),index<sortedListings.length-1){let dividerToUse=item.divider;dividerToUse||(dividerToUse=document.createElement("hr"),dividerToUse.className="listing-section-divider"),allItemsToRender.push(dividerToUse)}});const sortedIndices=new Set(sortedListings.map(item=>item.originalIndex)),unsortedListings=allListings.filter((item,index)=>!sortedIndices.has(index));if(sortedListings.length>0&&unsortedListings.length>0){const separatorDivider=document.createElement("hr");separatorDivider.className="listing-section-divider",allItemsToRender.push(separatorDivider)}unsortedListings.forEach((item,index)=>{if(allItemsToRender.push(item.element),index<unsortedListings.length-1){let dividerToUse=item.divider;dividerToUse||(dividerToUse=document.createElement("hr"),dividerToUse.className="listing-section-divider"),allItemsToRender.push(dividerToUse)}}),allItemsToRender.forEach(element=>{fragment.appendChild(element)}),insertionPoint.parentNode.insertBefore(fragment,insertionPoint.nextSibling),updateListingDividersForCard(salesCard)}function initCustomTooltipsForMarketplaces(){let tooltipContainer=document.getElementById("custom-tooltip-container");tooltipContainer||(tooltipContainer=document.createElement("div"),tooltipContainer.id="custom-tooltip-container",tooltipContainer.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 10002;
    `,document.body.appendChild(tooltipContainer)),document.querySelectorAll(".marketplace-col[data-tooltip]").forEach(col=>{const tooltipText=col.getAttribute("data-tooltip");if(!tooltipText)return;const customTooltip=document.createElement("div");customTooltip.className="custom-marketplace-tooltip",customTooltip.textContent=tooltipText,customTooltip.style.cssText=`
      position: absolute;
      background: #000000;
      color: #FFFFFF;
      padding: 6px 16px;
      border-radius: var(--tooltip-radius, 6px);
      font-family: 'Amazon Ember', sans-serif;
      font-size: var(--tooltip-font-size, 12px);
      font-weight: 500;
      white-space: nowrap;
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.2s ease, visibility 0.2s ease;
      z-index: 10003;
      pointer-events: none;
    `;const arrow=document.createElement("div");arrow.className="custom-tooltip-arrow",arrow.style.cssText=`
      position: absolute;
      bottom: -5px;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 0;
      border-left: 5px solid transparent;
      border-right: 5px solid transparent;
      border-top: 5px solid #000000;
    `,customTooltip.appendChild(arrow),tooltipContainer.appendChild(customTooltip);function showCustomTooltip(e){const rect=col.getBoundingClientRect(),tooltipRect=customTooltip.getBoundingClientRect(),left=rect.left+rect.width/2-tooltipRect.width/2,top=rect.top-tooltipRect.height-8;customTooltip.style.left=`${Math.max(10,Math.min(left,window.innerWidth-tooltipRect.width-10))}px`,customTooltip.style.top=`${Math.max(10,top)}px`,customTooltip.style.opacity="1",customTooltip.style.visibility="visible",activeCustomTooltips.set(col,{isVisible:!0,hideFunction:hideCustomTooltip,showFunction:showCustomTooltip})}function hideCustomTooltip(){customTooltip.style.opacity="0",customTooltip.style.visibility="hidden",activeCustomTooltips.delete(col)}const originalTooltipText=tooltipText;function handleMouseEnter(e){col.removeAttribute("data-tooltip"),showCustomTooltip(e)}function handleMouseLeave(e){col.setAttribute("data-tooltip",originalTooltipText),hideCustomTooltip()}function handleClick(e){hideCustomTooltip();const currentTooltip=col.getAttribute("data-tooltip");currentTooltip&&(col.removeAttribute("data-tooltip"),setTimeout(()=>{col.setAttribute("data-tooltip",currentTooltip)},100))}window.EventCleanupManager.addEventListener(col,"mouseenter",handleMouseEnter),window.EventCleanupManager.addEventListener(col,"mouseleave",handleMouseLeave),window.EventCleanupManager.addEventListener(col,"focus",handleMouseEnter),window.EventCleanupManager.addEventListener(col,"blur",handleMouseLeave),window.EventCleanupManager.addEventListener(col,"click",handleClick)})}function initAdSpendMarketplaceTooltips(){const getCurrentTimeframeLabel=()=>{const timeframeLabel=document.querySelector(".ad-spend-today-label");return timeframeLabel?timeframeLabel.textContent.trim():"Today's Ad Spend"},getACOSStatusLabel=acosValue=>{if(!acosValue||acosValue==="")return{label:"No Data",class:"no-data"};const numericAcos=parseFloat(acosValue);return isNaN(numericAcos)?{label:"No Data",class:"no-data"}:numericAcos<=15?{label:"Efficient",class:"efficient"}:numericAcos<=25?{label:"Average",class:"average"}:{label:"High",class:"high"}},getMarketplaceName=colElement=>colElement.classList.contains("ad-spend-marketplace-col-us")?"United States":colElement.classList.contains("ad-spend-marketplace-col-uk")?"United Kingdom":colElement.classList.contains("ad-spend-marketplace-col-de")?"Germany":colElement.classList.contains("ad-spend-marketplace-col-fr")?"France":colElement.classList.contains("ad-spend-marketplace-col-it")?"Italy":colElement.classList.contains("ad-spend-marketplace-col-es")?"Spain":colElement.classList.contains("ad-spend-marketplace-col-jp")?"Japan":"Unknown";if(document.querySelectorAll(".ad-spend-marketplace-col").forEach(col=>{document.querySelectorAll(".ad-spend-marketplace-tooltip").forEach(tooltip2=>{(tooltip2.dataset.parentCol===col.id||!tooltip2.dataset.parentCol)&&tooltip2.remove()});const flagImg=col.querySelector(".ad-spend-flag"),currencyValue=col.querySelector(".ad-spend-currency"),ordersValue=col.querySelector(".ad-spend-orders"),acosValue=col.querySelector(".ad-spend-acos-value");if(!flagImg||!currencyValue)return;const marketplaceName=getMarketplaceName(col),flagSrc=flagImg.src,currency=currencyValue.textContent.trim(),orders=ordersValue?ordersValue.textContent.replace(/[()]/g,""):"0",acos=acosValue?acosValue.textContent.trim():"",acosStatus=getACOSStatusLabel(acos),tooltip=document.createElement("div");tooltip.className="ad-spend-marketplace-tooltip",tooltip.dataset.parentCol=col.id||`ad-spend-marketplace-${Date.now()}-${Math.random()}`;const mockData={impressions:Math.floor(Math.random()*5e3)+1e3,clicks:Math.floor(Math.random()*50)+10,cpc:(Math.random()*2+.5).toFixed(2),cps:(Math.random()*5+2).toFixed(2)};let tooltipContent=`
      <div class="ad-spend-marketplace-tooltip-header">
        <img src="${flagSrc}" alt="${marketplaceName}" class="tooltip-flag" />
      </div>
      <div class="ad-spend-marketplace-tooltip-title">${getCurrentTimeframeLabel()}</div>
    `;tooltipContent+=`
      <div class="ad-spend-marketplace-tooltip-row">Impressions:&nbsp;<span>${mockData.impressions.toLocaleString()}</span></div>
      <div class="ad-spend-marketplace-tooltip-row">Orders:&nbsp;<span>${orders}</span></div>
    `,acos&&acos!==""&&(tooltipContent+=`
        <div class="ad-spend-marketplace-tooltip-row">
          ACOS:&nbsp;<span>${acos}%</span>&nbsp;<div class="acos-status-label ${acosStatus.class}">${acosStatus.label}</div>
        </div>
      `),tooltipContent+=`
      <div class="ad-spend-marketplace-tooltip-row">Clicks:&nbsp;<span>${mockData.clicks}</span></div>
      <div class="ad-spend-marketplace-tooltip-row">CPC:&nbsp;<span>$${mockData.cpc}</span></div>
      <div class="ad-spend-marketplace-tooltip-row">CPS:&nbsp;<span>$${mockData.cps}</span></div>
    `,tooltipContent+='<div class="ad-spend-marketplace-tooltip-arrow"></div>',tooltip.innerHTML=tooltipContent,tooltip.style.display="none",document.body.appendChild(tooltip);function showTooltip(){const timeframeElement=tooltip.querySelector(".ad-spend-marketplace-tooltip-title");timeframeElement&&(timeframeElement.textContent=getCurrentTimeframeLabel());const rect=col.getBoundingClientRect();tooltip.style.position="fixed",tooltip.style.left=rect.left+rect.width/2+"px",tooltip.style.bottom=window.innerHeight-rect.top+8+"px",tooltip.style.transform="translateX(-50%)",tooltip.style.display="block",tooltip.style.zIndex="10000000",activeCustomTooltips.set(col,{isVisible:!0,hideFunction:hideTooltip,showFunction:showTooltip})}function hideTooltip(){tooltip.style.display="none",activeCustomTooltips.delete(col)}window.EventCleanupManager.addEventListener(col,"mouseenter",showTooltip),window.EventCleanupManager.addEventListener(col,"mouseleave",hideTooltip),window.EventCleanupManager.addEventListener(col,"focus",showTooltip),window.EventCleanupManager.addEventListener(col,"blur",hideTooltip)}),!document.getElementById("ad-spend-marketplace-tooltip-style")){const style=document.createElement("style");style.id="ad-spend-marketplace-tooltip-style",style.textContent=`
      .ad-spend-marketplace-tooltip {
        position: fixed !important;
        background: #000000 !important;
        color: #FFFFFF !important;
        border-radius: 6px;
        padding: 12px 16px 8px 16px;
        font-family: 'Amazon Ember', Arial, sans-serif;
        font-size: var(--tooltip-font-size, 12px);
        font-weight: 500;
        z-index: 10000000 !important;
        min-width: 120px;
        text-align: left;
        line-height: 1.4;
        pointer-events: none;
        display: none;
        white-space: normal;
      }
      
      .ad-spend-marketplace-tooltip-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
      }
      
      .tooltip-flag {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        flex-shrink: 0;
      }
      
      .acos-status-label {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 2px 6px;
        border-radius: 3px;
        font-family: 'Amazon Ember', Arial, sans-serif;
        font-size: 9px;
        font-weight: 600;
        height: 14px;
        min-height: 14px;
        max-height: 14px;
        line-height: 1;
        text-align: center;
        width: fit-content;
        border: none;
        margin-left: 6px;
        text-transform: uppercase;
        letter-spacing: 0.3px;
      }
      
      .acos-status-label.efficient {
        background: rgba(4, 174, 44, 0.1);
        color: #04AE2C;
      }
      
      .acos-status-label.average {
        background: rgba(253, 195, 0, 0.1);
        color: #FDC300;
      }
      
      .acos-status-label.high {
        background: rgba(255, 57, 31, 0.1);
        color: #FF391F;
      }
      
      .acos-status-label.no-data {
        background: rgba(96, 111, 149, 0.1);
        color: #606F95;
      }
      
      .ad-spend-marketplace-tooltip-title {
        font-weight: 700;
        font-size: var(--tooltip-font-size, 12px);
        margin-bottom: 8px;
        color: var(--tooltip-text, #FFFFFF);
        line-height: 1.4;
      }
      
      .ad-spend-marketplace-tooltip-row {
        font-size: var(--tooltip-font-size, 12px);
        font-weight: 500;
        margin-bottom: 4px;
        display: flex;
        align-items: center;
        color: var(--tooltip-text, #FFFFFF);
        line-height: 1.4;
      }
      
      .ad-spend-marketplace-tooltip-row:last-child {
        margin-bottom: 0;
      }
      
      .ad-spend-marketplace-tooltip-arrow {
        position: absolute;
        bottom: -5px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid #000000;
      }
      
      /* Dark theme adjustments */
      [data-theme="dark"] .ad-spend-marketplace-tooltip {
        background: #000000 !important;
        color: #FFFFFF !important;
      }
      
      [data-theme="dark"] .ad-spend-marketplace-tooltip-arrow {
        border-top-color: #000000 !important;
      }
      
      [data-theme="dark"] .ad-spend-marketplace-tooltip-title,
      [data-theme="dark"] .ad-spend-marketplace-tooltip-row {
        color: #FFFFFF !important;
      }
    `,document.head.appendChild(style)}}window.initAdSpendMarketplaceTooltips=initAdSpendMarketplaceTooltips;function countNewSellerBadges(salesCard){const listingDivs=salesCard.querySelectorAll(".listing-analytics-div");let count=0;return listingDivs.forEach((listingDiv,index)=>{if(window.getComputedStyle(listingDiv).display!=="none"){const titleRow=listingDiv.querySelector(".listing-title-row"),foundInTitleRow=titleRow?titleRow.querySelector(".new-seller-badge")!==null:!1,foundAnywhere=listingDiv.querySelector(".new-seller-badge")!==null;(foundInTitleRow||foundAnywhere)&&count++}}),count}function updateNewTabCount(salesCard){const newTab=salesCard.querySelector(".new-tab .tab-label"),newTabContainer=salesCard.querySelector(".new-tab");if(newTab&&newTabContainer)setTimeout(()=>{const count=countNewSellerBadges(salesCard);count>=1?(newTab.innerHTML=`New <span class="count-green">(${count})</span>`,newTabContainer.classList.remove("no-new-sellers")):(newTab.textContent=`New (${count})`,newTabContainer.classList.add("no-new-sellers"))},10);else return}function initializeNewTabCounts(){document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div").forEach(salesCard=>{updateNewTabCount(salesCard)})}function refreshNewTabCounts(){document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div").forEach(salesCard=>{updateNewTabCount(salesCard)})}function handleRealTimeDataUpdate(newData){try{newData.listings&&updateListingsInDOM(newData.listings),newData.analytics&&updateAnalyticsData(newData.analytics),newData.accountStatus&&updateAccountStatusData(newData.accountStatus),newData.adSpend&&updateAdSpendData(newData.adSpend),newData.chartData&&(originalChartData&&(originalChartData=null),originalChartData=JSON.parse(JSON.stringify(newData.chartData)),updateChartForMarketplace(globalMarketplaceFocus)),refreshAllUIComponents()}catch{}}function updateListingsInDOM(listingsData){document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div").forEach((salesCard,cardIndex)=>{const cardListings=listingsData[cardIndex]||[],listingContainer=salesCard.querySelector(".listings-container")||salesCard,searchTabsDiv=salesCard.querySelector(".search-tabs-div"),existingListings=salesCard.querySelectorAll(".listing-analytics-div"),existingDividers=salesCard.querySelectorAll(".listing-section-divider");existingListings.forEach(listing=>listing.remove()),existingDividers.forEach(divider=>divider.remove()),cardListings.forEach((listing,index)=>{const listingHTML=generateListingHTML(listing),listingElement=createElementFromHTML(listingHTML);if(searchTabsDiv&&searchTabsDiv.nextSibling?searchTabsDiv.parentNode.insertBefore(listingElement,searchTabsDiv.nextSibling):listingContainer.appendChild(listingElement),updateListingProductImageBackground(listingElement),index<cardListings.length-1){const dividerElement=createElementFromHTML('<hr class="listing-section-divider" />');listingElement.parentNode.insertBefore(dividerElement,listingElement.nextSibling)}})}),hideBadgesWithZeroValues(),hideAdRowsWithZeroValues(),window.updateAllSalesCardsPadding&&setTimeout(()=>{window.updateAllSalesCardsPadding()},50)}function updateAnalyticsData(analyticsData){document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div").forEach((salesCard,cardIndex)=>{const cardAnalytics=analyticsData[cardIndex];if(!cardAnalytics)return;const salesCount=salesCard.querySelector(".sales-count");salesCount&&cardAnalytics.totalSales!==void 0&&(salesCount.textContent=cardAnalytics.totalSales);const analytics=calculateComprehensiveAnalytics(salesCard);updateMainAnalyticsSection(salesCard,analytics),updateAllMarketplaceColumns(salesCard,analytics),cardAnalytics.marketplaces&&updateMarketplaceTotals(salesCard,cardAnalytics.marketplaces)})}function updateAccountStatusData(accountStatusData){const accountStatus=document.querySelector(".account-status");if(!accountStatus||!accountStatusData)return;const tierValue=accountStatus.querySelector(".tier-value");tierValue&&accountStatusData.tier&&(tierValue.textContent=accountStatusData.tier),accountStatus.querySelectorAll(".metric-item").forEach((item,index)=>{const metric=accountStatusData.metrics&&accountStatusData.metrics[index];if(!metric)return;const percentage=item.querySelector(".metric-percentage"),progressFill=item.querySelector(".progress-fill"),subtext=item.querySelector(".metric-subtext");percentage&&(percentage.textContent=`${metric.percentage}%`),progressFill&&(progressFill.style.width=`${metric.percentage}%`),subtext&&(subtext.textContent=metric.subtext)}),calculateMetricRemainingValues()}function updateAdSpendData(adSpendData){const spendValue=document.querySelector(".ad-spend-header-value"),ordersValue=document.querySelectorAll(".ad-spend-header-value")[1];spendValue&&adSpendData.totalSpend&&(spendValue.textContent=adSpendData.totalSpend),ordersValue&&adSpendData.totalOrders&&(ordersValue.textContent=adSpendData.totalOrders);const marketplaceSelectors=[".ad-spend-marketplace-col-us",".ad-spend-marketplace-col-uk",".ad-spend-marketplace-col-de",".ad-spend-marketplace-col-fr",".ad-spend-marketplace-col-it",".ad-spend-marketplace-col-es",".ad-spend-marketplace-col-jp"];adSpendData.marketplaces&&marketplaceSelectors.forEach((selector,index)=>{const col=document.querySelector(selector),marketplaceData=adSpendData.marketplaces[index];if(!col||!marketplaceData)return;const currencyValue=col.querySelector(".ad-spend-currency"),ordersSpan=col.querySelector(".ad-spend-orders"),acosValue=col.querySelector(".ad-spend-acos-value");currencyValue&&(currencyValue.textContent=marketplaceData.currency),ordersSpan&&(ordersSpan.textContent=`(${marketplaceData.orders})`),acosValue&&(acosValue.textContent=marketplaceData.acos)})}function refreshAllUIComponents(){document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div").forEach((salesCard,cardIndex)=>{updateNewTabCount(salesCard);const currentMarketplace=getActiveMarketplaceForCard(cardIndex);currentMarketplace&&updateListingVisibility(salesCard,currentMarketplace);const searchInput=salesCard.querySelector(".search-input");if(searchInput&&searchInput.value.trim()){const searchTerm=searchInput.value.trim();}const activeTab=salesCard.querySelector(".sales-filter-tab.active");if(activeTab){const sortType=getSortTypeFromTab(activeTab),sortIcon=activeTab.querySelector(".tab-sort-icon"),direction=sortIcon&&sortIcon.src.includes("Descending")?"desc":"asc";sortListings(salesCard,sortType,direction)}initializeAdSpendStatus(),salesCard.querySelectorAll(".listing-analytics-div").forEach(listing=>{updateListingProductImageBackground(listing)})}),globalMarketplaceFocus!=="all"?applyMarketplaceFocus(globalMarketplaceFocus):updateChartForMarketplace("all"),hideBadgesWithZeroValues(),hideAdRowsWithZeroValues(),window.updateAllSalesCardsPadding&&window.updateAllSalesCardsPadding(),applyMarketplaceOpacityStyling(),applyMarketplaceConditionalStyling()}function createElementFromHTML(htmlString){const div=document.createElement("div");return div.innerHTML=htmlString.trim(),div.firstChild}function generateListingHTML(listingData){return'<div class="listing-analytics-div"><!-- Generated from listingData --></div>'}function performSearchForCard(salesCard,searchTerm){}window.handleRealTimeDataUpdate=handleRealTimeDataUpdate,window.updateListingsInDOM=updateListingsInDOM,window.updateAnalyticsData=updateAnalyticsData,window.updateAccountStatusData=updateAccountStatusData,window.updateAdSpendData=updateAdSpendData,window.initializeDynamicProductImageBackgrounds=initializeDynamicProductImageBackgrounds,window.updateListingProductImageBackground=updateListingProductImageBackground,window.triggerBackgroundColorUpdate=triggerBackgroundColorUpdate,window.stopRealTimeColorMonitoring=stopRealTimeColorMonitoring,window.hideBadgesWithZeroValues=hideBadgesWithZeroValues,window.hideAdRowsWithZeroValues=hideAdRowsWithZeroValues,window.adjustListingLayoutForHiddenAdRows=adjustListingLayoutForHiddenAdRows,window.testRealTimeColorUpdate=function(){const firstListing=document.querySelector(".listing-analytics-div");if(!firstListing)return;const orderedColorsBadge=firstListing.querySelector(".ordered-colors-badge");if(!orderedColorsBadge)return;const colorNumbers=orderedColorsBadge.querySelectorAll(".color-number");if(colorNumbers.length<2)return;colorNumbers.forEach((num,i)=>{});const secondColorNumber=colorNumbers[1],originalValue=secondColorNumber.textContent,newValue="10";secondColorNumber.textContent=newValue,setTimeout(()=>{secondColorNumber.textContent=originalValue},3e3)},window.testPercentageCalculations=function(){const salesCards=document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div");salesCards.length!==0&&salesCards.forEach((salesCard,index)=>{updateAllPercentageCalculations(salesCard,"all"),["us","uk","de"].forEach(marketplace=>{updateAllPercentageCalculations(salesCard,marketplace)}),updateAllPercentageCalculations(salesCard,"all")})},window.testFourSalesCardsPercentages=function(){document.querySelectorAll(".four-sales-cards-section .current-month-card-div, .four-sales-cards-section .last-month-card-div, .four-sales-cards-section .current-year-card-div, .four-sales-cards-section .last-year-card-div").length!==0&&(restoreFourSalesCardsToAllMarketplaces(),setTimeout(()=>{["us","uk","de","fr"].forEach(marketplace=>{filterFourSalesCardsByMarketplace(marketplace)}),setTimeout(()=>{restoreFourSalesCardsToAllMarketplaces()},1e3)},500))},window.testSalesDataRealism=function(){document.querySelectorAll(".four-sales-cards-section .current-month-card-div, .four-sales-cards-section .last-month-card-div, .four-sales-cards-section .current-year-card-div, .four-sales-cards-section .last-year-card-div").forEach((salesCard,index)=>{const salesCount=parseInt(salesCard.querySelector(".sales-count").textContent.replace(/,/g,"")),returnedValue=parseInt(salesCard.querySelector(".metric-value.returned").textContent.replace(/[^\d]/g,""))||0,newValue=parseInt(salesCard.querySelector(".metric-value.new").textContent.replace(/,/g,""))||0,adsValue=parseInt(salesCard.querySelector(".metric-value.ads").textContent.replace(/,/g,""))||0,cancelledValue=parseInt(salesCard.querySelector(".metric-value.cancelled").textContent.replace(/,/g,""))||0;returnedValue>salesCount||newValue>salesCount||adsValue>salesCount||cancelledValue>salesCount})},window.testColorOrderChange=function(){const firstListing=document.querySelector(".listing-analytics-div");if(!firstListing)return;const orderedColorsBadge=firstListing.querySelector(".ordered-colors-badge");if(!orderedColorsBadge)return;const colorItems=orderedColorsBadge.querySelectorAll(".color-item");let blueItem=null,greenItem=null;colorItems.forEach(item=>{const circle=item.querySelector(".color-circle"),number=item.querySelector(".color-number");if(circle&&number){const style=circle.getAttribute("style");style&&(style.includes("#0066CC")||style.includes("blue")?blueItem={circle,number,originalCount:number.textContent}:(style.includes("#00FF00")||style.includes("green"))&&(greenItem={circle,number,originalCount:number.textContent}))}}),!(!blueItem||!greenItem)&&(greenItem.number.textContent="5",setTimeout(()=>{greenItem.number.textContent=greenItem.originalCount},5e3))},window.refreshAllUIComponents=refreshAllUIComponents,window.fixMissingDividers=function(){document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div").forEach((salesCard,cardIndex)=>{ensureListingDividers(salesCard)})},window.refreshNewTabCounts=refreshNewTabCounts,window.debugNewTabCount=function(cardIndex=0){const salesCard=document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div")[cardIndex];if(!salesCard)return;const currentMarketplace=getActiveMarketplaceForCard(cardIndex),allListings=salesCard.querySelectorAll(".listing-analytics-div"),visibleListings=Array.from(allListings).filter(listing=>window.getComputedStyle(listing).display!=="none");let totalNewBadges=0,visibleNewBadges=0;allListings.forEach((listing,index)=>{const isVisible=window.getComputedStyle(listing).display!=="none",titleRow=listing.querySelector(".listing-title-row"),foundInTitleRow=titleRow?titleRow.querySelector(".new-seller-badge")!==null:!1,foundAnywhere=listing.querySelector(".new-seller-badge")!==null;(foundInTitleRow||foundAnywhere)&&(totalNewBadges++,isVisible&&visibleNewBadges++)});const newTab=salesCard.querySelector(".new-tab .tab-label"),currentDisplay=newTab?newTab.textContent:"NOT FOUND",countResult=countNewSellerBadges(salesCard);return updateNewTabCount(salesCard),setTimeout(()=>{const finalDisplay=newTab?newTab.textContent:"NOT FOUND"},50),{marketplace:currentMarketplace,totalListings:allListings.length,visibleListings:visibleListings.length,totalNewBadges,visibleNewBadges,countResult,currentDisplay}};function ensureListingDividers(salesCard){const listingDivs=salesCard.querySelectorAll(".listing-analytics-div");if(listingDivs.length<2)return;const listings=Array.from(listingDivs);for(let i=0;i<listings.length-1;i++){const currentListing=listings[i],nextListing=listings[i+1];let elementAfterCurrent=currentListing.nextElementSibling;for(;elementAfterCurrent&&!elementAfterCurrent.classList.contains("listing-analytics-div")&&!elementAfterCurrent.classList.contains("listing-section-divider");)elementAfterCurrent=elementAfterCurrent.nextElementSibling;if(elementAfterCurrent===nextListing){const divider=document.createElement("hr");divider.className="listing-section-divider",divider.style.display="block",currentListing.parentNode.insertBefore(divider,nextListing)}}}function checkIfListingMatchesMarketplace(flagSrc,marketplace){const marketplaceFlags={us:"US.svg",uk:"UK.svg",de:"DE.svg",fr:"FR.svg",it:"IT.svg",es:"ES.svg",jp:"JP.svg"};return flagSrc.includes(marketplaceFlags[marketplace])}window.dashboardComponent={render:initDashboard,html:dashboardHTML,css:"",initializeCustomTooltips};function initializeScrollbarDetection(){document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div").forEach(card=>{const scrollableContent=card.querySelector(".sales-scrollable-content");if(!scrollableContent)return;function updateScrollbarClass(){scrollableContent.scrollHeight>scrollableContent.clientHeight?(card.classList.add("scrollbar-visible"),card.classList.remove("scrollbar-hidden")):(card.classList.add("scrollbar-hidden"),card.classList.remove("scrollbar-visible"))}updateScrollbarClass(),new ResizeObserver(()=>{updateScrollbarClass()}).observe(scrollableContent),window.addEventListener("resize",updateScrollbarClass)})}document.addEventListener("DOMContentLoaded",initializeScrollbarDetection);const scrollbarObserver=new MutationObserver(mutations=>{mutations.forEach(mutation=>{mutation.type==="childList"&&mutation.addedNodes.length>0&&Array.from(mutation.addedNodes).filter(node=>node.nodeType===Node.ELEMENT_NODE&&(node.classList?.contains("todays-sales-card-div")||node.classList?.contains("yesterdays-sales-card-div")||node.classList?.contains("current-month-card-div")||node.classList?.contains("last-month-card-div")||node.classList?.contains("current-year-card-div")||node.classList?.contains("last-year-card-div")||node.querySelector?.(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div"))).length>0&&setTimeout(initializeScrollbarDetection,100)})});scrollbarObserver.observe(document.body,{childList:!0,subtree:!0});function initializeDynamicProductImageBackgrounds(){document.querySelectorAll(".listing-analytics-div").forEach(listing=>{updateListingProductImageBackground(listing)}),setupRealTimeColorMonitoring()}function updateSalesCardDates(){const today=window.SnapTimezone.getPacificTime(),yesterday=new Date(today);yesterday.setDate(today.getDate()-1);const lastWeekEnd=new Date(yesterday),lastWeekStart=new Date(lastWeekEnd);lastWeekStart.setDate(lastWeekEnd.getDate()-6);const currentMonthStart=new Date(today.getFullYear(),today.getMonth(),1),currentMonthEnd=new Date(today.getFullYear(),today.getMonth()+1,0),lastMonthStart=new Date(today.getFullYear(),today.getMonth()-1,1),lastMonthEnd=new Date(today.getFullYear(),today.getMonth(),0),currentYearStart=new Date(today.getFullYear(),0,1),currentYearEnd=new Date(today.getFullYear(),11,31),lastYearStart=new Date(today.getFullYear()-1,0,1),lastYearEnd=new Date(today.getFullYear()-1,11,31),formatOptions={month:"short",day:"numeric",year:"numeric"},todayFormatted=window.SnapTimezone.formatPacificDate(today,formatOptions),yesterdayFormatted=window.SnapTimezone.formatPacificDate(yesterday,formatOptions),lastWeekStartFormatted=window.SnapTimezone.formatPacificDate(lastWeekStart,formatOptions),lastWeekEndFormatted=window.SnapTimezone.formatPacificDate(lastWeekEnd,formatOptions),todaysSalesDate=document.getElementById("todays-sales-date");todaysSalesDate&&(todaysSalesDate.textContent=todayFormatted);const yesterdaysSalesDate=document.getElementById("yesterdays-sales-date");yesterdaysSalesDate&&(yesterdaysSalesDate.textContent=yesterdayFormatted);const lastWeekSalesDate=document.getElementById("last-week-sales-date");if(lastWeekSalesDate){const dateRange=`${lastWeekStartFormatted} to ${lastWeekEndFormatted}`;lastWeekSalesDate.textContent=dateRange}document.querySelectorAll(".four-sales-cards-section .sales-cards-row:first-child .current-month-card-div .sales-card-date").forEach(element=>{const startMonth=window.SnapTimezone.formatPacificDate(currentMonthStart,{month:"short"}),endMonth=window.SnapTimezone.formatPacificDate(currentMonthEnd,{month:"short"}),startDay=currentMonthStart.getDate(),endDay=currentMonthEnd.getDate(),year=currentMonthStart.getFullYear();let dateRange;startMonth===endMonth?dateRange=`${startMonth} ${startDay} to ${endMonth} ${endDay}, ${year}`:dateRange=`${startMonth} ${startDay} to ${endMonth} ${endDay}, ${year}`,element.textContent=dateRange}),document.querySelectorAll(".four-sales-cards-section .sales-cards-row:first-child .last-month-card-div .sales-card-date").forEach(element=>{const startMonth=window.SnapTimezone.formatPacificDate(lastMonthStart,{month:"short"}),endMonth=window.SnapTimezone.formatPacificDate(lastMonthEnd,{month:"short"}),startDay=lastMonthStart.getDate(),endDay=lastMonthEnd.getDate(),year=lastMonthStart.getFullYear();let dateRange;startMonth===endMonth?dateRange=`${startMonth} ${startDay} to ${endMonth} ${endDay}, ${year}`:dateRange=`${startMonth} ${startDay} to ${endMonth} ${endDay}, ${year}`,element.textContent=dateRange}),document.querySelectorAll(".four-sales-cards-section .sales-cards-row:last-child .current-year-card-div .sales-card-date").forEach(element=>{const startMonth=window.SnapTimezone.formatPacificDate(currentYearStart,{month:"short"}),endMonth=window.SnapTimezone.formatPacificDate(currentYearEnd,{month:"short"}),startDay=currentYearStart.getDate(),endDay=currentYearEnd.getDate(),year=currentYearStart.getFullYear(),dateRange=`${startMonth} ${startDay} to ${endMonth} ${endDay}, ${year}`;element.textContent=dateRange}),document.querySelectorAll(".four-sales-cards-section .sales-cards-row:last-child .last-year-card-div .sales-card-date").forEach(element=>{const startMonth=window.SnapTimezone.formatPacificDate(lastYearStart,{month:"short"}),endMonth=window.SnapTimezone.formatPacificDate(lastYearEnd,{month:"short"}),startDay=lastYearStart.getDate(),endDay=lastYearEnd.getDate(),year=lastYearStart.getFullYear(),dateRange=`${startMonth} ${startDay} to ${endMonth} ${endDay}, ${year}`;element.textContent=dateRange})}const MARKETPLACE_DISTRIBUTION={us:.35,uk:.2,de:.15,fr:.12,it:.08,es:.06,jp:.04},MARKETPLACE_CURRENCIES={us:"$",uk:"\xA3",de:"\u20AC",fr:"\u20AC",it:"\u20AC",es:"\u20AC",jp:"\xA5"};function calculateComparisonPercentage(currentValue,previousValue){if(previousValue===0||previousValue===null||previousValue===void 0)return{percentage:null,isPositive:!1,isValid:!1,message:"No previous data available"};if(currentValue==null||isNaN(currentValue)||isNaN(previousValue))return{percentage:null,isPositive:!1,isValid:!1,message:"Missing or invalid data"};const percentageChange=(currentValue-previousValue)/Math.abs(previousValue)*100,roundedPercentage=Math.round(percentageChange*10)/10;return{percentage:roundedPercentage,isPositive:roundedPercentage>=0,isValid:!0,message:null}}function calculateMarketplaceAwareComparison(salesCard,marketplace,periodData){let currentValue,previousValue;if(marketplace==="all")currentValue=periodData.totalSales||0,previousValue=periodData.previousSales||0;else if(salesCard.closest(".four-sales-cards-section")!==null){const marketplaceData=periodData.marketplaces&&periodData.marketplaces[marketplace];if(marketplaceData){currentValue=marketplaceData.sales||0;const marketplaceDistribution=marketplaceData.sales/(periodData.totalSales||1);previousValue=Math.round((periodData.previousSales||0)*marketplaceDistribution)}else currentValue=0,previousValue=0}else{currentValue=calculateMarketplaceSpecificAnalytics(salesCard,marketplace).totalUnits||0;const marketplaceDistribution=getMarketplaceDistribution(salesCard,marketplace);previousValue=Math.round((periodData.previousSales||0)*marketplaceDistribution)}return calculateComparisonPercentage(currentValue,previousValue)}function getMarketplaceDistribution(salesCard,marketplace){const allAnalytics=calculateComprehensiveAnalytics(salesCard),marketplaceAnalytics=calculateMarketplaceSpecificAnalytics(salesCard,marketplace);return allAnalytics.totalUnits===0?0:marketplaceAnalytics.totalUnits/allAnalytics.totalUnits}function getComparisonLabel(period){const currentDate=window.SnapTimezone.getPacificTime(),currentYear=currentDate.getFullYear(),currentMonth=currentDate.getMonth();switch(period){case"currentMonth":{const prevMonth=currentMonth===0?11:currentMonth-1,prevYear=currentMonth===0?currentYear-1:currentYear;return`Compared to ${new Date(prevYear,prevMonth,1).toLocaleDateString("en-US",{month:"long"})} ${prevYear}`}case"lastMonth":{let targetMonth=currentMonth-2,targetYear=currentYear;for(;targetMonth<0;)targetMonth+=12,targetYear-=1;return`Compared to ${new Date(targetYear,targetMonth,1).toLocaleDateString("en-US",{month:"long"})} ${targetYear}`}case"currentYear":return`Compared to ${currentYear-1}`;case"lastYear":return`Compared to ${currentYear-2}`;default:return"Compared to previous period"}}function updateComparisonContainer(salesCard,comparisonData,comparisonLabel,marketplace="all"){const comparisonContainer=salesCard.querySelector(".comparison-container");if(!comparisonContainer)return;const comparisonContent=comparisonContainer.querySelector(".comparison-content"),comparisonPercentage=comparisonContainer.querySelector(".comparison-percentage"),comparisonArrow=comparisonContainer.querySelector(".comparison-arrow"),comparisonLabelElement=comparisonContainer.querySelector(".comparison-label");if(!comparisonContent||!comparisonPercentage||!comparisonArrow||!comparisonLabelElement)return;if(!comparisonData||!comparisonData.isValid){comparisonContainer.style.display="none";return}comparisonContainer.style.display="flex";let percentageText;comparisonData.percentage===null||comparisonData.percentage===void 0||isNaN(comparisonData.percentage)?percentageText="0.0%":percentageText=comparisonData.isPositive?`+${Math.abs(comparisonData.percentage)}%`:`-${Math.abs(comparisonData.percentage)}%`;const numericPct=parseFloat(comparisonData.percentage);if(!isNaN(numericPct)&&numericPct===0){comparisonContainer.style.display="none";return}comparisonPercentage.textContent=percentageText,comparisonPercentage.classList.remove("positive","negative"),comparisonPercentage.classList.add(comparisonData.isPositive?"positive":"negative");const arrowSrc=comparisonData.isPositive?"./assets/up-per-ic.svg":"./assets/down-per-ic.svg";comparisonArrow.src=arrowSrc,comparisonArrow.alt=comparisonData.isPositive?"Up":"Down";const contextualLabel=marketplace==="all"?comparisonLabel:`${comparisonLabel} (${marketplace.toUpperCase()})`;comparisonLabelElement.textContent=contextualLabel}function updateAllPercentageCalculations(salesCard,marketplace="all",periodData=null){try{if(salesCard.closest(".four-sales-cards-section")!==null){if(periodData){const comparisonData=calculateMarketplaceAwareComparison(salesCard,marketplace,periodData),comparisonLabel=getComparisonLabel(periodData.period||"currentMonth");updateComparisonContainer(salesCard,comparisonData,comparisonLabel,marketplace)}}else{let analytics;if(marketplace==="all"?analytics=calculateComprehensiveAnalytics(salesCard):analytics=calculateMarketplaceSpecificAnalytics(salesCard,marketplace),updateMainAnalyticsSection(salesCard,analytics),periodData){const comparisonData=calculateMarketplaceAwareComparison(salesCard,marketplace,periodData),comparisonLabel=getComparisonLabel(periodData.period||"currentMonth");updateComparisonContainer(salesCard,comparisonData,comparisonLabel,marketplace)}}}catch{}}async function generateFourSalesCardsMockDataAsync(){return new Promise(resolve=>{setTimeout(()=>{try{const data=generateFourSalesCardsMockData();resolve(data)}catch{resolve({})}},0)})}function generateFourSalesCardsMockData(useConsistentData=!0){const baseSalesData={currentMonth:2847,lastMonth:4523,currentYear:28947,lastYear:45234},previousPeriodData={currentMonth:4523,lastMonth:3987,currentYear:45234,lastYear:38765},mockData={};return Object.keys(baseSalesData).forEach(period=>{const totalSales=baseSalesData[period],previousSales=previousPeriodData[period],marketplaceData={};Object.keys(MARKETPLACE_DISTRIBUTION).forEach(marketplace=>{const basePercentage=MARKETPLACE_DISTRIBUTION[marketplace];let adjustedPercentage;if(useConsistentData)adjustedPercentage=basePercentage;else{const randomFactor=.8+Math.random()*.4;adjustedPercentage=basePercentage*randomFactor}let sales=Math.round(totalSales*adjustedPercentage);const zeroSalesMarketplaces={currentMonth:"fr",lastMonth:"es",currentYear:"jp",lastYear:"it"};marketplace===zeroSalesMarketplaces[period]&&(sales=0),!useConsistentData&&marketplace!==zeroSalesMarketplaces[period]&&Math.random()<.1&&marketplace!=="us"&&(sales=0);let royalties,returnRate,returned;useConsistentData?(royalties=sales*3.5,returnRate=.025,returned=Math.round(sales*returnRate)):(royalties=sales*(2.5+Math.random()*2),returnRate=Math.random()*.05,returned=Math.round(sales*returnRate)),marketplaceData[marketplace]={sales,royalties,returned}});const currentTotal=Object.values(marketplaceData).reduce((sum,data)=>sum+data.sales,0),difference=totalSales-currentTotal;marketplaceData.us.sales+=difference,mockData[period]={totalSales,previousSales,marketplaces:marketplaceData,period,lastUpdated:Date.now()}}),mockData}async function applyFourSalesCardsMockData(){const fourSalesCardsSection=document.querySelector(".four-sales-cards-section");fourSalesCardsSection&&window.SnapLoader&&(fourSalesCardsSection.style.position="relative",fourSalesCardsSection.style.borderRadius="14px",window.SnapLoader.showOverlay(fourSalesCardsSection,{text:"Loading sales data...",id:"four-sales-cards-loader"}));try{const mockData=await generateFourSalesCardsMockDataAsync(),cardPeriods=["currentMonth","lastMonth","currentYear","lastYear"],fourSalesCards=document.querySelectorAll(".four-sales-cards-section .current-month-card-div, .four-sales-cards-section .last-month-card-div, .four-sales-cards-section .current-year-card-div, .four-sales-cards-section .last-year-card-div");if(fourSalesCards.length===0){fourSalesCardsSection&&window.SnapLoader&&window.SnapLoader.hideOverlay(fourSalesCardsSection);return}fourSalesCards.forEach((salesCard,cardIndex)=>{const period=cardPeriods[cardIndex],periodData=mockData[period];if(!periodData)return;const salesCount=salesCard.querySelector(".sales-count");salesCount&&(salesCount.textContent=periodData.totalSales.toLocaleString(),salesCount.classList.remove("zero")),Object.keys(periodData.marketplaces).forEach(marketplace=>{const marketplaceData=periodData.marketplaces[marketplace],marketplaceCol=salesCard.querySelector(`.marketplace-col.${marketplace}`);marketplaceCol&&updateFourSalesCardMarketplaceColumn(marketplaceCol,marketplaceData,marketplace)}),updateFourSalesCardAnalytics(salesCard,periodData);const currentMarketplace=globalMarketplaceFocus||"all",comparisonData=calculateMarketplaceAwareComparison(salesCard,currentMarketplace,periodData),comparisonLabel=getComparisonLabel(period);updateComparisonContainer(salesCard,comparisonData,comparisonLabel,currentMarketplace)}),setTimeout(()=>{applyMarketplaceOpacityStyling(),applyMarketplaceConditionalStyling()},100)}catch{}finally{fourSalesCardsSection&&window.SnapLoader&&window.SnapLoader.hideOverlay(fourSalesCardsSection)}}function updateFourSalesCardMarketplaceColumn(marketplaceCol,data,marketplace){const salesCountElement=marketplaceCol.querySelector(".marketplace-total-sales-count");salesCountElement&&(salesCountElement.textContent=data.sales.toLocaleString(),salesCountElement.classList.toggle("zero",data.sales===0));const royaltiesElement=marketplaceCol.querySelector(".marketplace-total-earned-royalties");if(royaltiesElement){const currency=MARKETPLACE_CURRENCIES[marketplace],formattedRoyalties=data.royalties.toLocaleString("en-US",{minimumFractionDigits:1,maximumFractionDigits:1});royaltiesElement.textContent=`${currency}${formattedRoyalties}`,royaltiesElement.classList.remove("zero","negative"),data.royalties===0?royaltiesElement.classList.add("zero"):data.royalties<0&&royaltiesElement.classList.add("negative")}const returnedElement=marketplaceCol.querySelector(".marketplace-total-returned-units");returnedElement&&(returnedElement.textContent=formatReturnedUnits(data.returned),returnedElement.classList.remove("zero","negative"),data.returned===0?returnedElement.classList.add("zero"):data.returned>0&&returnedElement.classList.add("negative"))}function updateFourSalesCardAnalytics(salesCard,periodData){const displayedSalesCount=Object.keys(periodData.marketplaces).length===1?periodData.filteredSales:periodData.totalSales,totals=Object.values(periodData.marketplaces).reduce((acc,data)=>(acc.royalties+=data.royalties,acc.returned+=data.returned,acc),{royalties:0,returned:0}),royaltiesElement=salesCard.querySelector(".metric-value.royalties");if(royaltiesElement){const formattedRoyalties=totals.royalties.toLocaleString("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2});royaltiesElement.textContent=formattedRoyalties,royaltiesElement.classList.remove("zero","negative"),totals.royalties===0?royaltiesElement.classList.add("zero"):totals.royalties<0&&royaltiesElement.classList.add("negative")}const returnedElement=salesCard.querySelector(".metric-value.returned"),returnedPercentageElement=salesCard.querySelector(".metric-percentage.returned-percentage");if(returnedElement&&(returnedElement.textContent=formatReturnedMetric(totals.returned),returnedElement.classList.remove("zero","negative"),totals.returned===0?returnedElement.classList.add("zero"):totals.returned>0&&returnedElement.classList.add("negative"),returnedPercentageElement))if(totals.returned>0&&displayedSalesCount>0){const returnedPercentage=(totals.returned/displayedSalesCount*100).toFixed(1);returnedPercentageElement.textContent=`${returnedPercentage}%`,returnedPercentageElement.style.display="block"}else returnedPercentageElement.style.display="none",returnedPercentageElement.classList.add("zero");const cancelledElement=salesCard.querySelector(".metric-value.cancelled");if(cancelledElement){const cancelled=Math.round(displayedSalesCount*.002);cancelledElement.textContent=formatNumberWithCommas(cancelled),cancelledElement.classList.toggle("zero",cancelled===0),cancelledElement.classList.toggle("has-value",cancelled>0)}const newElement=salesCard.querySelector(".metric-value.new");if(newElement){const newListings=Math.round(displayedSalesCount*.15);newElement.textContent=formatNumberWithCommas(newListings),newElement.classList.toggle("zero",newListings===0),newElement.classList.toggle("has-value",newListings>0)}const adsElement=salesCard.querySelector(".metric-value.ads");if(adsElement){const ads=Math.round(displayedSalesCount*.25);adsElement.textContent=formatNumberWithCommas(ads),adsElement.classList.toggle("zero",ads===0),adsElement.classList.toggle("has-value",ads>0)}const newPercentageElement=salesCard.querySelector(".metric-percentage.new-percentage");if(newPercentageElement){const newListings=Math.round(displayedSalesCount*.15);if(newListings===0)newPercentageElement.style.display="none",newPercentageElement.classList.add("zero");else if(displayedSalesCount>0){const newPercentage=(newListings/displayedSalesCount*100).toFixed(1);newPercentageElement.textContent=`${newPercentage}%`,newPercentageElement.style.display="block"}}const adsPercentageElement=salesCard.querySelector(".metric-percentage.ads-percentage");if(adsPercentageElement){const ads=Math.round(displayedSalesCount*.25);if(ads===0)adsPercentageElement.style.display="none",adsPercentageElement.classList.add("zero");else if(displayedSalesCount>0){const adsPercentage=(ads/displayedSalesCount*100).toFixed(1);adsPercentageElement.textContent=`${adsPercentage}%`,adsPercentageElement.style.display="block"}}enforceZeroPercentageVisibility(salesCard)}function applyMarketplaceConditionalStyling(){document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div, .top-day-card-div, .top-week-card-div, .top-month-card-div, .top-year-card-div").forEach(salesCard=>{salesCard.querySelectorAll(".marketplace-total-sales-count").forEach(salesElement=>{const salesText=salesElement.textContent.trim(),numericValue=parseInt(salesText.replace(/[^\d]/g,""))||0;salesElement.classList.remove("zero","negative"),numericValue===0?salesElement.classList.add("zero"):numericValue<0&&salesElement.classList.add("negative")}),salesCard.querySelectorAll(".marketplace-total-earned-royalties").forEach(royaltiesElement=>{const royaltiesText=royaltiesElement.textContent.trim(),numericValue=parseFloat(royaltiesText.replace(/[^-0-9.]/g,""))||0;royaltiesElement.classList.remove("zero","negative"),numericValue===0?royaltiesElement.classList.add("zero"):numericValue<0&&royaltiesElement.classList.add("negative")}),salesCard.querySelectorAll(".marketplace-total-returned-units").forEach(returnedElement=>{const match=returnedElement.textContent.trim().match(/\((-?\d+)\)/),numericValue=match?parseInt(match[1]):0;returnedElement.classList.remove("zero","negative"),numericValue===0?returnedElement.classList.add("zero"):numericValue<0&&returnedElement.classList.add("negative")}),salesCard.querySelectorAll(".metric-percentage").forEach(percentageElement=>{const percentageText=percentageElement.textContent.trim(),numericValue=parseFloat(percentageText.replace("%",""))||0;percentageElement.classList.remove("zero","negative"),numericValue===0?(percentageElement.classList.add("zero"),percentageElement.style.display="none"):(percentageElement.style.display="",numericValue<0&&percentageElement.classList.add("negative"))}),salesCard.querySelectorAll(".metric-value").forEach(metricElement=>{const metricText=metricElement.textContent.trim();let numericValue=0;if(metricElement.classList.contains("returned")){const match=metricText.match(/\((-?[\d,]+)\)/);match?numericValue=parseInt(match[1].replace(/,/g,""))||0:numericValue=parseInt(metricText.replace(/[^\d-]/g,""))||0}else if(metricElement.classList.contains("royalties"))numericValue=parseFloat(metricText.replace(/[^-0-9.]/g,""))||0;else{const cleanText=metricText.replace(/[^\d-]/g,"");numericValue=parseInt(cleanText)||0}metricElement.classList.remove("zero","negative"),numericValue===0?metricElement.classList.add("zero"):numericValue<0&&metricElement.classList.add("negative")})})}function applyMarketplaceOpacityStyling(){document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div, .top-day-card-div, .top-week-card-div, .top-month-card-div, .top-year-card-div").forEach((salesCard,cardIndex)=>{const marketplaceCols=salesCard.querySelectorAll(".marketplace-col:not(.all-marketplaces)");marketplaceCols.length>0&&marketplaceCols.forEach(marketplaceCol=>{const salesCountElement=marketplaceCol.querySelector(".marketplace-total-sales-count");if(salesCountElement){const salesText=salesCountElement.textContent.replace(/,/g,""),salesCount=parseInt(salesText)||0,royaltiesElement=marketplaceCol.querySelector(".marketplace-total-earned-royalties"),returnedElement=marketplaceCol.querySelector(".marketplace-total-returned-units");let royaltiesValue=0,returnedValue=0;if(royaltiesElement){const royaltiesText=royaltiesElement.textContent.trim();royaltiesValue=parseFloat(royaltiesText.replace(/[^-0-9.]/g,""))||0}if(returnedElement){const match=returnedElement.textContent.trim().match(/\((-?\d+)\)/);returnedValue=match?parseInt(match[1]):0}salesCount===0&&royaltiesValue===0&&returnedValue===0?marketplaceCol.classList.add("zero-sales"):marketplaceCol.classList.remove("zero-sales"),marketplaceCol.style.removeProperty("opacity")}})})}function initializeMarketplaceOpacityMonitoring(){const observer=new MutationObserver(mutations=>{let shouldUpdate=!1;mutations.forEach(mutation=>{if(mutation.type==="childList"||mutation.type==="characterData"){const target=mutation.target;target.classList&&(target.classList.contains("marketplace-total-sales-count")||target.classList.contains("marketplace-total-earned-royalties")||target.classList.contains("marketplace-total-returned-units")||target.classList.contains("metric-value")||target.classList.contains("metric-percentage"))&&(shouldUpdate=!0),target.querySelector&&(target.querySelector(".marketplace-total-sales-count")||target.querySelector(".marketplace-total-earned-royalties")||target.querySelector(".marketplace-total-returned-units")||target.querySelector(".metric-value")||target.querySelector(".metric-percentage"))&&(shouldUpdate=!0)}}),shouldUpdate&&(clearTimeout(window.marketplaceOpacityTimeout),window.marketplaceOpacityTimeout=setTimeout(()=>{applyMarketplaceOpacityStyling(),applyMarketplaceConditionalStyling()},100))}),sectionsToObserve=[{selector:".sales-cards-container",name:"Today's/Yesterday's cards"},{selector:".four-sales-cards-section",name:"four sales cards"},{selector:".lifetime-insights-card-div",name:"lifetime insights card"},{selector:".customer-reviews-card-div",name:"customer reviews card"},{selector:".top-day-card-div",name:"top day card"},{selector:".top-week-card-div",name:"top week card"},{selector:".top-month-card-div",name:"top month card"},{selector:".top-year-card-div",name:"top year card"}];let observedSections=0;sectionsToObserve.forEach(section=>{const element=document.querySelector(section.selector);element&&(observer.observe(element,{childList:!0,subtree:!0,characterData:!0}),observedSections++)}),window.marketplaceOpacityObserver=observer}async function refreshFourSalesCardsData(){await applyFourSalesCardsMockData()}class FourSalesCardsRealTimeManager{constructor(){this.isActive=!1,this.refreshInterval=null,this.refreshFrequency=3e4,this.lastRefreshTime=0,this.errorCount=0,this.maxErrors=5}start(frequency=3e4){this.isActive||(this.refreshFrequency=frequency,this.isActive=!0,this.errorCount=0,this.refreshData(),window.EventCleanupManager?this.refreshInterval=window.EventCleanupManager.setInterval(()=>this.refreshData(),this.refreshFrequency):this.refreshInterval=setInterval(()=>this.refreshData(),this.refreshFrequency))}stop(){this.isActive&&(this.isActive=!1,this.refreshInterval&&(window.EventCleanupManager&&window.EventCleanupManager.clearInterval?window.EventCleanupManager.clearInterval(this.refreshInterval):clearInterval(this.refreshInterval),this.refreshInterval=null))}async refreshData(){try{const startTime=performance.now(),mockData=generateFourSalesCardsMockData(!0),cardPeriods=["currentMonth","lastMonth","currentYear","lastYear"];let fourSalesCards=document.querySelectorAll(".four-sales-cards-section .sales-card");if(fourSalesCards.length===0&&(fourSalesCards=document.querySelectorAll(".four-sales-cards-section .current-month-card-div, .four-sales-cards-section .last-month-card-div, .four-sales-cards-section .current-year-card-div, .four-sales-cards-section .last-year-card-div")),fourSalesCards.length===0)return;fourSalesCards.forEach((salesCard,cardIndex)=>{const period=cardPeriods[cardIndex],periodData=mockData[period];if(periodData){const salesNumberElement=salesCard.querySelector(".sales-number");salesNumberElement&&(salesNumberElement.textContent=periodData.totalSales.toLocaleString());const currentMarketplace=globalMarketplaceFocus||"all",comparisonData=calculateMarketplaceAwareComparison(salesCard,currentMarketplace,periodData),comparisonLabel=getComparisonLabel(period);updateComparisonContainer(salesCard,comparisonData,comparisonLabel,currentMarketplace)}}),this.lastRefreshTime=Date.now(),this.errorCount=0;const endTime=performance.now(),event=new CustomEvent("fourSalesCardsRefreshed",{detail:{timestamp:this.lastRefreshTime,data:mockData,refreshTime:endTime-startTime}});document.dispatchEvent(event)}catch{this.errorCount++,this.errorCount>=this.maxErrors&&this.stop()}}async manualRefresh(){await this.refreshData()}getStatus(){return{isActive:this.isActive,refreshFrequency:this.refreshFrequency,lastRefreshTime:this.lastRefreshTime,errorCount:this.errorCount,timeSinceLastRefresh:this.lastRefreshTime?Date.now()-this.lastRefreshTime:null}}}window.FourSalesCardsRealTimeManager=new FourSalesCardsRealTimeManager;function generateTopFourSalesCardsMockData(useConsistentData=!0){const baseSalesData={topDay:6834,topWeek:6834,topMonth:4523,topYear:15830},mockData={};return Object.keys(baseSalesData).forEach(period=>{const baseSales=baseSalesData[period],royaltiesPerUnit=1.4+Math.random()*1.2,returnRate=Math.random()*.03+.01,newRate=Math.random()*.06+.04,adsRate=Math.random()*.03+.015,zeroReturned=Math.random()<.15,zeroNew=Math.random()<.2,zeroAds=Math.random()<.2,royalties=Math.round(baseSales*royaltiesPerUnit),returned=zeroReturned?0:Math.round(baseSales*returnRate),cancelled=Math.round(baseSales*.0013),newListings=zeroNew?0:Math.round(baseSales*newRate),ads=zeroAds?0:Math.round(baseSales*adsRate);mockData[period]={totalSales:baseSales,totalRoyalties:royalties,totalReturned:returned,totalCancelled:cancelled,totalNew:newListings,totalAds:ads,returnedPercentage:baseSales?(returned/baseSales*100).toFixed(1):"0.0",newPercentage:baseSales?(newListings/baseSales*100).toFixed(1):"0.0",adsPercentage:baseSales?(ads/baseSales*100).toFixed(1):"0.0"}}),mockData}async function applyTopFourSalesCardsMockData(){try{const mockData=generateTopFourSalesCardsMockData(!0),cardPeriods=["topDay","topWeek","topMonth","topYear"],cardSelectors=[".top-day-card-div",".top-week-card-div",".top-month-card-div",".top-year-card-div"],marketplaceData={topDay:{us:{sales:943,royalties:1558,returned:18},uk:{sales:187,royalties:203,returned:0},de:{sales:89,royalties:120,returned:0},fr:{sales:64,royalties:98,returned:3},it:{sales:28,royalties:38,returned:5},es:{sales:41,royalties:52,returned:1},jp:{sales:0,royalties:0,returned:0}},topWeek:{us:{sales:6167,royalties:10276,returned:98},uk:{sales:1002,royalties:1403,returned:0},de:{sales:589,royalties:825,returned:0},fr:{sales:412,royalties:545,returned:16},it:{sales:176,royalties:247,returned:14},es:{sales:210,royalties:260,returned:8},jp:{sales:0,royalties:0,returned:0}},topMonth:{us:{sales:21876,royalties:36459,returned:348},uk:{sales:4334,royalties:6067,returned:0},de:{sales:2067,royalties:2894,returned:0},fr:{sales:1876,royalties:2640,returned:45},it:{sales:770,royalties:1078,returned:50},es:{sales:1345,royalties:1870,returned:32},jp:{sales:0,royalties:0,returned:0}},topYear:{us:{sales:141426,royalties:235360,returned:4879},uk:{sales:27547,royalties:38566,returned:0},de:{sales:13567,royalties:18994,returned:0},fr:{sales:12456,royalties:17980,returned:380},it:{sales:5094,royalties:7131,returned:1400},es:{sales:9765,royalties:14030,returned:300},jp:{sales:0,royalties:0,returned:0}}};window.TopFourMockData=mockData,window.TopFourMarketplaceData=marketplaceData,cardSelectors.forEach((selector,index)=>{const salesCard=document.querySelector(selector),period=cardPeriods[index],periodData=mockData[period],periodMarketplaceData=marketplaceData[period];if(salesCard&&periodData){const salesCountElement=salesCard.querySelector(".sales-count");salesCountElement&&(salesCountElement.textContent=formatNumberWithCommas(periodData.totalSales));const royaltiesElement=salesCard.querySelector(".metric-value.royalties");royaltiesElement&&(royaltiesElement.textContent=`$${formatNumberWithCommas(periodData.totalRoyalties)}.0`,royaltiesElement.classList.remove("zero","negative"),periodData.totalRoyalties===0?royaltiesElement.classList.add("zero"):periodData.totalRoyalties<0&&royaltiesElement.classList.add("negative"));const returnedElement=salesCard.querySelector(".metric-value.returned");returnedElement&&(returnedElement.textContent=formatReturnedMetric(periodData.totalReturned),returnedElement.classList.remove("zero","negative"),periodData.totalReturned===0?returnedElement.classList.add("zero"):periodData.totalReturned>0&&returnedElement.classList.add("negative"));const returnedPercentageElement=salesCard.querySelector(".returned-percentage");if(returnedPercentageElement){const returnedPct=parseFloat(periodData.returnedPercentage);!isNaN(returnedPct)&&returnedPct===0||periodData.totalReturned===0||periodData.totalSales===0?(returnedPercentageElement.style.display="none",returnedPercentageElement.classList.add("zero"),returnedPercentageElement.textContent="0.0%"):(returnedPercentageElement.textContent=`${periodData.returnedPercentage}%`,returnedPercentageElement.style.display="block",returnedPercentageElement.classList.remove("zero"))}const cancelledElement=salesCard.querySelector(".metric-value.cancelled");cancelledElement&&(cancelledElement.textContent=formatNumberWithCommas(periodData.totalCancelled),cancelledElement.classList.toggle("zero",periodData.totalCancelled===0),cancelledElement.classList.toggle("has-value",periodData.totalCancelled>0));const newElement=salesCard.querySelector(".metric-value.new");newElement&&(newElement.textContent=formatNumberWithCommas(periodData.totalNew),newElement.classList.toggle("zero",periodData.totalNew===0),newElement.classList.toggle("has-value",periodData.totalNew>0));const newPercentageElement=salesCard.querySelector(".new-percentage");if(newPercentageElement){const newPct=parseFloat(periodData.newPercentage);!isNaN(newPct)&&newPct===0||periodData.totalNew===0||periodData.totalSales===0?(newPercentageElement.style.display="none",newPercentageElement.classList.add("zero"),newPercentageElement.textContent="0.0%"):(newPercentageElement.textContent=`${periodData.newPercentage}%`,newPercentageElement.style.display="block",newPercentageElement.classList.remove("zero"))}const adsElement=salesCard.querySelector(".metric-value.ads");adsElement&&(adsElement.textContent=formatNumberWithCommas(periodData.totalAds),adsElement.classList.toggle("zero",periodData.totalAds===0),adsElement.classList.toggle("has-value",periodData.totalAds>0));const adsPercentageElement=salesCard.querySelector(".ads-percentage");if(adsPercentageElement){const adsPct=parseFloat(periodData.adsPercentage);!isNaN(adsPct)&&adsPct===0||periodData.totalAds===0||periodData.totalSales===0?(adsPercentageElement.style.display="none",adsPercentageElement.classList.add("zero"),adsPercentageElement.textContent="0.0%"):(adsPercentageElement.textContent=`${periodData.adsPercentage}%`,adsPercentageElement.style.display="block",adsPercentageElement.classList.remove("zero"))}periodMarketplaceData&&updateTopFourSalesCardMarketplaces(salesCard,periodMarketplaceData),enforceZeroPercentageVisibility(salesCard)}}),setTimeout(()=>{applyMarketplaceOpacityStyling(),applyMarketplaceConditionalStyling()},100)}catch{}}function updateTopFourSalesCardMarketplaces(salesCard,marketplaceData){const currencies={us:"$",uk:"\xA3",de:"\u20AC",fr:"\u20AC",it:"\u20AC",es:"\u20AC",jp:"\xA5"};Object.keys(marketplaceData).forEach(marketplace=>{const data=marketplaceData[marketplace],marketplaceCol=salesCard.querySelector(`.marketplace-col.${marketplace}`);if(marketplaceCol){const salesCountElement=marketplaceCol.querySelector(".marketplace-total-sales-count");salesCountElement&&(salesCountElement.textContent=data.sales.toLocaleString(),salesCountElement.classList.toggle("zero",data.sales===0));const royaltiesElement=marketplaceCol.querySelector(".marketplace-total-earned-royalties");if(royaltiesElement){const currency=currencies[marketplace]||"$",formattedRoyalties=data.royalties.toLocaleString("en-US",{minimumFractionDigits:1,maximumFractionDigits:1});royaltiesElement.textContent=`${currency}${formattedRoyalties}`,royaltiesElement.classList.remove("zero","negative"),data.royalties===0?royaltiesElement.classList.add("zero"):data.royalties<0&&royaltiesElement.classList.add("negative")}const returnedElement=marketplaceCol.querySelector(".marketplace-total-returned-units");returnedElement&&(returnedElement.textContent=formatReturnedUnits(data.returned),returnedElement.classList.remove("zero","negative"),data.returned===0?returnedElement.classList.add("zero"):data.returned>0&&returnedElement.classList.add("negative"))}})}function updateTopFourCardsForMarketplace(selectedMarketplace,updateToken=null){try{const cards=document.querySelectorAll(".top-four-sales-cards-section .top-day-card-div, .top-four-sales-cards-section .top-week-card-div, .top-four-sales-cards-section .top-month-card-div, .top-four-sales-cards-section .top-year-card-div");if(cards.length===0||!(window.TopFourMockData&&window.TopFourMarketplaceData))return;const periods=["topDay","topWeek","topMonth","topYear"];cards.forEach((card,idx)=>{if(updateToken&&window.marketplaceUpdateSeq&&updateToken!==window.marketplaceUpdateSeq)return;const periodKey=periods[idx],baseData=window.TopFourMockData[periodKey];if(selectedMarketplace==="all"){if(updateToken&&window.marketplaceUpdateSeq&&updateToken!==window.marketplaceUpdateSeq)return;const salesCount2=card.querySelector(".sales-count");salesCount2&&baseData&&(salesCount2.textContent=baseData.totalSales.toLocaleString());const mpData2=window.TopFourMarketplaceData[periodKey];mpData2&&updateTopFourSalesCardMarketplaces(card,mpData2),enforceZeroPercentageVisibility(card),refreshCardNoDataUI(card,!!(baseData&&baseData.totalSales));return}const mpData=window.TopFourMarketplaceData[periodKey]&&window.TopFourMarketplaceData[periodKey][selectedMarketplace];if(!mpData)return;const salesCount=card.querySelector(".sales-count");salesCount&&(salesCount.textContent=(mpData.sales||0).toLocaleString(),salesCount.classList.toggle("zero",(mpData.sales||0)===0)),updateTopFourSalesCardMarketplaces(card,{[selectedMarketplace]:mpData});const royaltiesEl=card.querySelector(".metric-value.royalties");if(royaltiesEl){const currency={us:"$",uk:"\xA3",de:"\u20AC",fr:"\u20AC",it:"\u20AC",es:"\u20AC",jp:"\xA5"}[selectedMarketplace]||"$";royaltiesEl.textContent=`${currency}${(mpData.royalties||0).toLocaleString("en-US",{minimumFractionDigits:1,maximumFractionDigits:1})}`,royaltiesEl.classList.toggle("zero",(mpData.royalties||0)===0),royaltiesEl.classList.toggle("negative",(mpData.royalties||0)<0)}const returnedEl=card.querySelector(".metric-value.returned"),returnedPctEl=card.querySelector(".metric-percentage.returned-percentage");if(returnedEl){const ret=mpData.returned||0;returnedEl.textContent=ret===0?"0":`(-${Math.abs(ret).toLocaleString()})`,returnedEl.classList.toggle("zero",ret===0),returnedEl.classList.toggle("negative",ret>0)}if(returnedPctEl){const salesForCalc2=mpData.sales||0,returnsForCalc=mpData.returned||0;if(!salesForCalc2||!returnsForCalc)returnedPctEl.style.display="none",returnedPctEl.classList.add("zero"),returnedPctEl.textContent="0.0%";else{const pct=(returnsForCalc/salesForCalc2*100).toFixed(1);returnedPctEl.textContent=`${pct}%`,returnedPctEl.style.display="block",returnedPctEl.classList.remove("zero")}}const salesForCalc=mpData.sales||0,cancelledEl=card.querySelector(".metric-value.cancelled");if(cancelledEl){const cancelled=Math.round(salesForCalc*.0013);cancelledEl.textContent=cancelled.toLocaleString(),cancelledEl.classList.toggle("zero",cancelled===0),cancelledEl.classList.toggle("has-value",cancelled>0)}const newEl=card.querySelector(".metric-value.new"),newPctEl=card.querySelector(".metric-percentage.new-percentage");if(newEl){const newListings=Math.round(salesForCalc*.07);if(newEl.textContent=newListings.toLocaleString(),newEl.classList.toggle("zero",newListings===0),newEl.classList.toggle("has-value",newListings>0),newPctEl)if(newListings===0||salesForCalc===0)newPctEl.style.display="none",newPctEl.classList.add("zero"),newPctEl.textContent="0.0%";else{const pct=(newListings/salesForCalc*100).toFixed(1);newPctEl.textContent=`${pct}%`,newPctEl.style.display="block",newPctEl.classList.remove("zero")}}const adsEl=card.querySelector(".metric-value.ads"),adsPctEl=card.querySelector(".metric-percentage.ads-percentage");if(adsEl){const ads=Math.round(salesForCalc*.028);if(adsEl.textContent=ads.toLocaleString(),adsEl.classList.toggle("zero",ads===0),adsEl.classList.toggle("has-value",ads>0),adsPctEl)if(ads===0||salesForCalc===0)adsPctEl.style.display="none",adsPctEl.classList.add("zero"),adsPctEl.textContent="0.0%";else{const pct=(ads/salesForCalc*100).toFixed(1);adsPctEl.textContent=`${pct}%`,adsPctEl.style.display="block",adsPctEl.classList.remove("zero")}}enforceZeroPercentageVisibility(card),refreshCardNoDataUI(card,(mpData.sales||0)>0)})}catch{}}window.refreshFourSalesCardsData=refreshFourSalesCardsData,window.applyMarketplaceOpacityStyling=applyMarketplaceOpacityStyling,window.applyMarketplaceConditionalStyling=applyMarketplaceConditionalStyling,window.generateFourSalesCardsMockData=generateFourSalesCardsMockData,window.applyFourSalesCardsMockData=applyFourSalesCardsMockData,window.generateTopFourSalesCardsMockData=generateTopFourSalesCardsMockData,window.applyTopFourSalesCardsMockData=applyTopFourSalesCardsMockData,window.updateMarketplaceStyling=function(){applyMarketplaceOpacityStyling(),applyMarketplaceConditionalStyling()},window.testTopFourSalesCards=function(){const mockData=generateTopFourSalesCardsMockData(!0);applyTopFourSalesCardsMockData(),[".top-day-card-div",".top-week-card-div",".top-month-card-div",".top-year-card-div"].forEach(selector=>{const card=document.querySelector(selector);if(card){const newElement=card.querySelector(".metric-value.new"),adsElement=card.querySelector(".metric-value.ads"),cancelledElement=card.querySelector(".metric-value.cancelled")}})},window.testFourSalesCardsZeroData=function(){const mockData=generateFourSalesCardsMockData(!0);applyFourSalesCardsMockData();const cardSelectors=[".current-month-card-div",".last-month-card-div",".current-year-card-div",".last-year-card-div"],cardPeriods=["currentMonth","lastMonth","currentYear","lastYear"];cardSelectors.forEach((selector,index)=>{const card=document.querySelector(selector),period=cardPeriods[index],periodData=mockData[period];if(card&&periodData){const marketplaceCols=card.querySelectorAll(".marketplace-col:not(.all-marketplaces)");let zeroSalesFound=!1;marketplaceCols.forEach(col=>{const salesElement=col.querySelector(".marketplace-total-sales-count"),royaltiesElement=col.querySelector(".marketplace-total-earned-royalties"),returnedElement=col.querySelector(".marketplace-total-returned-units");salesElement&&(parseInt(salesElement.textContent.replace(/,/g,""))||0)===0&&(zeroSalesFound=!0)})}}),setTimeout(()=>{applyMarketplaceOpacityStyling(),applyMarketplaceConditionalStyling()},100)},window.testTopFourSalesCardsZeroData=function(){applyTopFourSalesCardsMockData();const cardSelectors=[".top-day-card-div",".top-week-card-div",".top-month-card-div",".top-year-card-div"],cardPeriods=["topDay","topWeek","topMonth","topYear"];cardSelectors.forEach((selector,index)=>{const card=document.querySelector(selector),period=cardPeriods[index];if(card){const marketplaceCols=card.querySelectorAll(".marketplace-col:not(.all-marketplaces)");let zeroSalesFound=!1;marketplaceCols.forEach(col=>{const salesElement=col.querySelector(".marketplace-total-sales-count"),royaltiesElement=col.querySelector(".marketplace-total-earned-royalties"),returnedElement=col.querySelector(".marketplace-total-returned-units");salesElement&&(parseInt(salesElement.textContent.replace(/,/g,""))||0)===0&&(zeroSalesFound=!0)})}}),setTimeout(()=>{applyMarketplaceOpacityStyling(),applyMarketplaceConditionalStyling()},100)},window.debugFourSalesCards=function(){document.querySelectorAll(".four-sales-cards-section .current-month-card-div, .four-sales-cards-section .last-month-card-div, .four-sales-cards-section .current-year-card-div, .four-sales-cards-section .last-year-card-div").forEach((card,index)=>{const salesCount=card.querySelector(".sales-count"),marketplaceCols=card.querySelectorAll(".marketplace-col")})},window.fourSalesCardsControls={startRealTimeUpdates:function(frequency=3e4){window.FourSalesCardsRealTimeManager&&window.FourSalesCardsRealTimeManager.start(frequency)},stopRealTimeUpdates:function(){window.FourSalesCardsRealTimeManager&&window.FourSalesCardsRealTimeManager.stop()},refreshNow:async function(){window.FourSalesCardsRealTimeManager&&await window.FourSalesCardsRealTimeManager.manualRefresh()},getStatus:function(){return window.FourSalesCardsRealTimeManager?window.FourSalesCardsRealTimeManager.getStatus():null},testPercentageCalculations:function(){const mockData=generateFourSalesCardsMockData(!0);["currentMonth","lastMonth","currentYear","lastYear"].forEach(period=>{const periodData=mockData[period];if(periodData){const comparisonData=calculateComparisonPercentage(periodData.totalSales,periodData.previousSales)}})}},window.debugMarketplaceOpacity=function(){document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div").forEach((card,cardIndex)=>{card.querySelectorAll(".marketplace-col:not(.all-marketplaces)").forEach((col,colIndex)=>{const salesCountElement=col.querySelector(".marketplace-total-sales-count"),salesCount=salesCountElement?parseInt(salesCountElement.textContent.replace(/,/g,""))||0:"N/A",isActive=col.classList.contains("active"),isInactive=col.classList.contains("inactive"),isZeroSales=col.classList.contains("zero-sales"),computedOpacity=window.getComputedStyle(col).opacity,computedFilter=window.getComputedStyle(col).filter})})};function getMarketplaceSalesMultiplier(marketplaceCode){return{all:1,us:1.2,uk:.8,de:.9,fr:.6,it:.5,es:.4,jp:.7}[marketplaceCode]||1}function filterFourSalesCardsByMarketplace(marketplace){const fourSalesCards=document.querySelectorAll(".four-sales-cards-section .current-month-card-div, .four-sales-cards-section .last-month-card-div, .four-sales-cards-section .current-year-card-div, .four-sales-cards-section .last-year-card-div");if(fourSalesCards.length===0)return;const mockData=generateFourSalesCardsMockData(),cardPeriods=["currentMonth","lastMonth","currentYear","lastYear"];fourSalesCards.forEach((salesCard,cardIndex)=>{const period=cardPeriods[cardIndex],periodData=mockData[period];if(!periodData)return;const marketplaceData=periodData.marketplaces[marketplace];if(!marketplaceData)return;const salesCount=salesCard.querySelector(".sales-count");salesCount&&(salesCount.textContent=marketplaceData.sales.toLocaleString(),salesCount.classList.toggle("zero",marketplaceData.sales===0)),salesCard.querySelectorAll(".marketplace-col").forEach(col=>{Array.from(col.classList).find(cls=>["us","uk","de","fr","it","es","jp"].includes(cls))===marketplace?(col.style.display="flex",updateFourSalesCardMarketplaceColumn(col,marketplaceData,marketplace)):col.style.display="none"});const filteredPeriodData={totalSales:periodData.totalSales,previousSales:periodData.previousSales,filteredSales:marketplaceData.sales,period,marketplaces:{[marketplace]:marketplaceData}};updateFourSalesCardAnalytics(salesCard,filteredPeriodData);const comparisonData=calculateMarketplaceAwareComparison(salesCard,marketplace,filteredPeriodData),comparisonLabel=getComparisonLabel(period);updateComparisonContainer(salesCard,comparisonData,comparisonLabel,marketplace)}),setTimeout(()=>{applyMarketplaceOpacityStyling(),applyMarketplaceConditionalStyling()},100)}async function restoreFourSalesCardsToAllMarketplaces(){await applyFourSalesCardsMockData();const fourSalesCards=document.querySelectorAll(".four-sales-cards-section .current-month-card-div, .four-sales-cards-section .last-month-card-div, .four-sales-cards-section .current-year-card-div, .four-sales-cards-section .last-year-card-div");fourSalesCards.forEach((salesCard,cardIndex)=>{salesCard.querySelectorAll(".marketplace-col:not(.all-marketplaces)").forEach(col=>{col.style.display=""})});const mockData=generateFourSalesCardsMockData(),cardPeriods=["currentMonth","lastMonth","currentYear","lastYear"];fourSalesCards.forEach((salesCard,cardIndex)=>{const period=cardPeriods[cardIndex],periodData=mockData[period];if(periodData){const comparisonData=calculateMarketplaceAwareComparison(salesCard,"all",periodData),comparisonLabel=getComparisonLabel(period);updateComparisonContainer(salesCard,comparisonData,comparisonLabel,"all")}})}function updateTodayVsPreviousYearsDate(){const dateElement=document.getElementById("today-vs-previous-years-date");if(dateElement){const today=window.SnapTimezone.getPacificTime(),formattedDate=window.SnapTimezone.formatPacificDate(today,{month:"short",day:"numeric"});dateElement.textContent=formattedDate}}function updateMonthlySalesDate(){const dateElement=document.getElementById("monthly-sales-date");if(dateElement){const currentYear=window.SnapTimezone.getPacificTime().getFullYear(),formattedDate=`January ${currentYear} to December ${currentYear}`;dateElement.textContent=formattedDate}}function updateYearlySalesDate(){const dateElement=document.getElementById("yearly-sales-date");if(dateElement){const formattedDate=`2000 to ${window.SnapTimezone.getPacificTime().getFullYear()}`;dateElement.textContent=formattedDate}}function generateTodayVsPreviousYearsData(){const data=[],pacificMonthDay=window.SnapTimezone.getPacificMonthDay(),todayMonth=pacificMonthDay.month,todayDay=pacificMonthDay.day,marketplaceCodes=["US","UK","DE","FR","IT","ES","JP"];for(let year=2e3;year<=2025;year++){const marketplaces=[];let totalSales=0,totalRoyalties=0,totalReturns=0;const yearDate=new Date(year,todayMonth,todayDay),monthAbbreviation=yearDate.toLocaleDateString("en-US",{month:"short"}),day=yearDate.getDate(),yearAbbreviation=year.toString().slice(-2),monthDay=`${monthAbbreviation} ${day}`,yearLabel=`'${yearAbbreviation}`;marketplaceCodes.forEach(code=>{let baseSalesMin,baseSalesMax;year<=2005?(baseSalesMin=15,baseSalesMax=50):year<=2010?(baseSalesMin=25,baseSalesMax=70):year<=2015?(baseSalesMin=35,baseSalesMax=80):year<=2020?(baseSalesMin=45,baseSalesMax=90):(baseSalesMin=25,baseSalesMax=90);const marketplaceMultipliers={US:1,UK:.7,DE:.6,FR:.5,IT:.4,ES:.35,JP:.3},baseSales=Math.floor(Math.random()*(baseSalesMax-baseSalesMin+1))+baseSalesMin,adjustedBaseSales=Math.floor(baseSales*marketplaceMultipliers[code]),sales=Math.random()<.125?Math.floor(Math.random()*51)+150:Math.max(adjustedBaseSales,10),royalties=Math.floor(sales*.15),returns=Math.floor(sales*.05);marketplaces.push({code,sales,royalties,returns}),totalSales+=sales,totalRoyalties+=royalties,totalReturns+=returns});const dataPoint={month:monthDay,monthDay,yearLabel,year:year.toString().slice(-2),marketplaces,sales:totalSales,royalties:totalRoyalties,returns:totalReturns,values:marketplaces.map(mp=>mp.sales),labels:marketplaces.map(mp=>mp.code)};data.push(dataPoint)}return data}function filterTodayVsPreviousYearsDataByMarketplace(data,marketplace){if(marketplace==="all")return data;const marketplaceCode=marketplace.toUpperCase();return data.map(dataPoint=>{const targetMarketplace=dataPoint.marketplaces.find(mp=>mp.code===marketplaceCode);return targetMarketplace?{...dataPoint,marketplaces:[targetMarketplace],sales:targetMarketplace.sales,royalties:targetMarketplace.royalties,returns:targetMarketplace.returns,values:[targetMarketplace.sales],labels:[targetMarketplace.code]}:{...dataPoint,marketplaces:[],sales:0,royalties:0,returns:0,values:[],labels:[]}})}function initializeLazyLoadedCharts(){const lastWeekContainer=document.querySelector("#last-week-chart-container");lastWeekContainer&&window.ViewportLazyLoader.registerComponent(lastWeekContainer,{id:"last-week-sales-chart",type:"chart",loadingText:"Loading last week sales data...",dataGenerator:async()=>null,initFunction:async(element,data)=>{const skeleton=element.querySelector(".chart-skeleton");skeleton&&skeleton.remove(),await initializeLastWeekSalesChart()}});const todayVsPreviousContainer=document.querySelector("#today-vs-previous-years-chart-container");todayVsPreviousContainer&&window.ViewportLazyLoader.registerComponent(todayVsPreviousContainer,{id:"today-vs-previous-years-chart",type:"chart",loadingText:"Loading 26 years of data...",dataGenerator:async()=>null,initFunction:async(element,data)=>{const skeleton=element.querySelector(".chart-skeleton");skeleton&&skeleton.remove(),await initializeTodayVsPreviousYearsChart()}});const monthlySalesContainer=document.querySelector("#monthly-sales-chart-container");monthlySalesContainer&&window.ViewportLazyLoader.registerComponent(monthlySalesContainer,{id:"monthly-sales-chart",type:"chart",loadingText:"Loading monthly sales data...",dataGenerator:async()=>null,initFunction:async(element,data)=>{const skeleton=element.querySelector(".chart-skeleton");skeleton&&skeleton.remove(),await initializeMonthlySalesChart()}});const yearlySalesContainer=document.querySelector("#yearly-sales-chart-container");yearlySalesContainer&&window.ViewportLazyLoader.registerComponent(yearlySalesContainer,{id:"yearly-sales-chart",type:"chart",loadingText:"Loading yearly sales data...",dataGenerator:async()=>null,initFunction:async(element,data)=>{const skeleton=element.querySelector(".chart-skeleton");skeleton&&skeleton.remove(),await initializeYearlySalesChart()}})}async function generateTodayVsPreviousYearsDataAsync(){return new Promise(resolve=>{setTimeout(()=>{try{const data=generateTodayVsPreviousYearsData();resolve(data)}catch{resolve([])}},0)})}async function initializeTodayVsPreviousYearsChart(){const chartContainer=document.getElementById("today-vs-previous-years-chart-container");if(chartContainer){window.SnapLoader&&window.SnapLoader.showOverlay(chartContainer,{text:"Loading chart data...",id:"today-vs-previous-years-loader"});try{chartContainer.style.position="relative",chartContainer.style.borderRadius="14px",window.SnapLoader&&window.SnapLoader.showOverlay(chartContainer,{text:"Generating 26 years of data...",id:"today-vs-previous-years-data-loader"});const chartData=await generateTodayVsPreviousYearsDataAsync();window.todayVsPreviousYearsOriginalData||(window.todayVsPreviousYearsOriginalData=JSON.parse(JSON.stringify(chartData)));const chart=new SnapChart({container:chartContainer,type:"scrollable-stacked-column",data:chartData,demoOptions:{showContainer:!1,showTitle:!1,showDataEditor:!1,showControls:!1,showInsights:!1},options:{responsive:!0,animate:!0,height:300,isTodayVsPreviousYearsChart:!0,fullWidthDistribution:!0}});chartContainer.snapChart=chart;try{const showReturns=sessionStorage.getItem("showReturns_today-vs-previous")==="true",showRoyalties=sessionStorage.getItem("showRoyalties_today-vs-previous")==="true";chart.options.showReturns=showReturns,chart.options.showRoyalties=showRoyalties,(!showReturns||!showRoyalties)&&chart.render()}catch{}setTimeout(()=>{chart&&chart.setupTodayVsPreviousYearsScrolling&&chart.setupTodayVsPreviousYearsScrolling()},100),window.SnapLoader&&(window.SnapLoader.hideOverlay(chartContainer),window.SnapLoader.hideOverlay("today-vs-previous-years-data-loader"))}catch{window.SnapLoader&&window.SnapLoader.hideOverlay(chartContainer)}}}function updateTodayVsPreviousYearsChartForMarketplace(marketplace){const chartContainer=document.getElementById("today-vs-previous-years-chart-container");if(!(!chartContainer||!chartContainer.snapChart))try{const chart=chartContainer.snapChart,originalData=window.todayVsPreviousYearsOriginalData;if(!originalData)return;const filteredData=filterTodayVsPreviousYearsDataByMarketplace(originalData,marketplace);chart.updateData(filteredData),setTimeout(()=>{chart&&chart.setupTodayVsPreviousYearsScrolling&&chart.setupTodayVsPreviousYearsScrolling()},100)}catch{}}function generateRandomDaySales(daysAgo=0){const allMarketplaces=["US","UK","DE","FR","IT","ES","JP"],numActiveMarketplaces=Math.floor(Math.random()*7)+1,activeMarketplaces=[...allMarketplaces].sort(()=>.5-Math.random()).slice(0,numActiveMarketplaces),salesData={};return allMarketplaces.forEach(marketplace=>{if(activeMarketplaces.includes(marketplace)){const sales=Math.floor(Math.random()*91)+5;salesData[marketplace]={sales,returns:Math.random()<.7?0:Math.floor(Math.random()*Math.max(1,sales*.1))}}else salesData[marketplace]={sales:0,returns:0}}),salesData}async function generateLastWeekSalesDataAsync(){return new Promise(resolve=>{setTimeout(()=>{try{const now=new Date,pacificTime=new Date(now.toLocaleString("en-US",{timeZone:"America/Los_Angeles"})),today=new Date(pacificTime),endDate=new Date(today);endDate.setDate(today.getDate()-1);const data=[];for(let i=6;i>=0;i--){const currentDate=new Date(endDate);currentDate.setDate(endDate.getDate()-i);const salesForDay=generateRandomDaySales(i),marketplaces=[{code:"US",sales:salesForDay.US.sales,royalties:Math.floor(salesForDay.US.sales*.35),returns:salesForDay.US.returns},{code:"UK",sales:salesForDay.UK.sales,royalties:Math.floor(salesForDay.UK.sales*.35),returns:salesForDay.UK.returns},{code:"DE",sales:salesForDay.DE.sales,royalties:Math.floor(salesForDay.DE.sales*.35),returns:salesForDay.DE.returns},{code:"FR",sales:salesForDay.FR.sales,royalties:Math.floor(salesForDay.FR.sales*.35),returns:salesForDay.FR.returns},{code:"IT",sales:salesForDay.IT.sales,royalties:Math.floor(salesForDay.IT.sales*.35),returns:salesForDay.IT.returns},{code:"ES",sales:salesForDay.ES.sales,royalties:Math.floor(salesForDay.ES.sales*.35),returns:salesForDay.ES.returns},{code:"JP",sales:salesForDay.JP.sales,royalties:Math.floor(salesForDay.JP.sales*.35),returns:salesForDay.JP.returns}],dayData={month:currentDate.toLocaleDateString("en-US",{month:"short"}).toUpperCase(),day:currentDate.getDate().toString().padStart(2,"0"),year:currentDate.getFullYear().toString().slice(-2),marketplaces,sales:marketplaces.reduce((sum,mp)=>sum+mp.sales,0),royalties:marketplaces.reduce((sum,mp)=>sum+mp.royalties,0),returns:marketplaces.reduce((sum,mp)=>sum+mp.returns,0),values:marketplaces.map(mp=>mp.sales),labels:marketplaces.map(mp=>mp.code)};data.push(dayData)}resolve(data)}catch{resolve([])}},0)})}async function initializeLastWeekSalesChart(){const chartContainer=document.querySelector("#last-week-chart-container");if(!chartContainer)return;chartContainer.style.position="relative",chartContainer.style.borderRadius="14px",window.SnapLoader&&window.SnapLoader.showOverlay(chartContainer,{text:"Loading chart data...",id:"last-week-chart-loader"}),window.SnapLoader&&window.SnapLoader.showOverlay(chartContainer,{text:"Generating last week sales data...",id:"last-week-sales-data-loader"});const now=new Date,pacificTime=new Date(now.toLocaleString("en-US",{timeZone:"America/Los_Angeles"})),today=new Date(pacificTime),endDate=new Date(today);endDate.setDate(today.getDate()-1);const startDate=new Date(endDate);startDate.setDate(endDate.getDate()-6);const prevEndDate=new Date(startDate);prevEndDate.setDate(startDate.getDate()-1);const prevStartDate=new Date(prevEndDate);prevStartDate.setDate(prevEndDate.getDate()-6);const currentPeriodData=await generateLastWeekSalesDataAsync(),previousPeriodData=await generateLastWeekSalesDataAsync(),lastWeekData=currentPeriodData;originalChartData=JSON.parse(JSON.stringify(lastWeekData)),window.lastWeekOriginalData=JSON.parse(JSON.stringify(lastWeekData));try{const startDateStr=startDate.toLocaleDateString("en-US",{month:"long",day:"numeric",year:"numeric"}),endDateStr=endDate.toLocaleDateString("en-US",{month:"long",day:"numeric",year:"numeric"}),prevStartDateStr=prevStartDate.toLocaleDateString("en-US",{month:"long",day:"numeric",year:"numeric"}),prevEndDateStr=prevEndDate.toLocaleDateString("en-US",{month:"long",day:"numeric",year:"numeric"}),chart=new SnapChart({container:chartContainer,type:"stacked-column",data:lastWeekData,demoOptions:{showContainer:!1,showTitle:!1,showDataEditor:!1,showControls:!1,showInsights:!1},options:{responsive:!0,animate:!0,height:300,compareMode:!1,compareData:previousPeriodData}});chartContainer.snapChart=chart;try{const showReturns=sessionStorage.getItem("showReturns_monthly")==="true",showRoyalties=sessionStorage.getItem("showRoyalties_monthly")==="true";chart.options.showReturns=showReturns,chart.options.showRoyalties=showRoyalties,(!showReturns||!showRoyalties)&&chart.render()}catch{}try{const showReturns=sessionStorage.getItem("showReturns_last-week")==="true",showRoyalties=sessionStorage.getItem("showRoyalties_last-week")==="true";chart.options.showReturns=showReturns,chart.options.showRoyalties=showRoyalties,(!showReturns||!showRoyalties)&&chart.render()}catch{}initializeLastWeekSalesButtons(),window.SnapLoader&&(window.SnapLoader.hideOverlay(chartContainer),window.SnapLoader.hideOverlay("last-week-sales-data-loader"))}catch{window.SnapLoader&&(window.SnapLoader.hideOverlay(chartContainer),window.SnapLoader.hideOverlay("last-week-sales-data-loader"))}}async function generateMonthlySalesDataForYearAsync(year){return new Promise(resolve=>{setTimeout(()=>{try{const data=generateMonthlySalesDataForYear(year);resolve(data)}catch{resolve([])}},0)})}async function initializeMonthlySalesChart(){const chartContainer=document.querySelector("#monthly-sales-chart-container");if(!chartContainer)return;window.SnapLoader&&window.SnapLoader.showOverlay(chartContainer,{text:"Loading chart data...",id:"monthly-sales-chart-loader"});const currentYear=window.SnapTimezone.getPacificTime().getFullYear();chartContainer.style.position="relative",chartContainer.style.borderRadius="14px",window.SnapLoader&&window.SnapLoader.showOverlay(chartContainer,{text:"Generating monthly sales data...",id:"monthly-sales-data-loader"});const monthlyData=await generateMonthlySalesDataForYearAsync(currentYear);originalMonthlySalesData=JSON.parse(JSON.stringify(monthlyData));const chartType=monthlyData.length>12?"scrollable-stacked-column":"stacked-column";try{const chart=new SnapChart({container:chartContainer,type:chartType,data:monthlyData,demoOptions:{showContainer:!1,showTitle:!1,showDataEditor:!1,showControls:!1,showInsights:!1},options:{responsive:!0,animate:!0,height:300,width:null,compareMode:!1,fullWidthDistribution:!0,xAxis:{type:"category",labels:{format:"MMM, 'YY"}}}});chartContainer.snapChart=chart,chartContainer.chartState={mode:"single-year",selectedYear:currentYear},chartContainer.dataset.monthlyMode="year";try{const showReturns=sessionStorage.getItem("showReturns_monthly")==="true",showRoyalties=sessionStorage.getItem("showRoyalties_monthly")==="true";chart.options.showReturns=showReturns,chart.options.showRoyalties=showRoyalties,(showReturns||showRoyalties)&&chart.render()}catch{}try{renderQuarterlySummary(currentYear),attachQuarterlySummaryHook(chartContainer)}catch{}window.SnapLoader&&(window.SnapLoader.hideOverlay(chartContainer),window.SnapLoader.hideOverlay("monthly-sales-data-loader"))}catch{window.SnapLoader&&window.SnapLoader.hideOverlay(chartContainer)}}function filterChartDataByMarketplace(chartData,marketplace){return!chartData||!Array.isArray(chartData)?chartData||[]:chartData.map((dayData,index)=>{if(!dayData||typeof dayData!="object")return dayData;const allMarketplaces=dayData.marketplaces||[],validMarketplaces=allMarketplaces.filter(mp=>mp&&typeof mp=="object"&&mp.code&&typeof mp.sales=="number");validMarketplaces.length,allMarketplaces.length;let processedMarketplaces=[],processedValues=[],processedLabels=[];if(marketplace==="all")processedMarketplaces=validMarketplaces,processedValues=validMarketplaces.map(mp=>mp.sales||0),processedLabels=validMarketplaces.map(mp=>mp.code||"UNKNOWN");else{const normalizedMarketplace=marketplace.toLowerCase(),selectedMarketplace=validMarketplaces.find(mp=>mp.code&&mp.code.toLowerCase()===normalizedMarketplace);if(selectedMarketplace)processedMarketplaces=[selectedMarketplace],processedValues=[selectedMarketplace.sales||0],processedLabels=[selectedMarketplace.code];else{const marketplaceCode=marketplace.toUpperCase();processedMarketplaces=[{code:marketplaceCode,sales:0,royalties:0,returns:0}],processedValues=[0],processedLabels=[marketplaceCode]}}const totalSales=processedMarketplaces.reduce((sum,mp)=>sum+(mp.sales||0),0),totalRoyalties=processedMarketplaces.reduce((sum,mp)=>sum+(mp.royalties||0),0),totalReturns=processedMarketplaces.reduce((sum,mp)=>sum+(mp.returns||0),0);return{...dayData,marketplaces:processedMarketplaces,sales:totalSales,royalties:totalRoyalties,returns:totalReturns,change:totalReturns,values:processedValues,labels:processedLabels,_filterMetadata:{originalMarketplaceCount:allMarketplaces.length,filteredMarketplaceCount:processedMarketplaces.length,filterType:marketplace==="all"?"all":"single",appliedMarketplace:marketplace}}})}function updateChartForMarketplace(marketplace){const updateResults={lastWeek:!1,monthly:!1,yearly:!1,todayVsPrevious:!1},lastWeekChartContainer=document.querySelector("#last-week-chart-container");if(lastWeekChartContainer&&lastWeekChartContainer.snapChart&&originalChartData)try{const chart=lastWeekChartContainer.snapChart;if(!Array.isArray(originalChartData)||originalChartData.length===0)throw new Error("Invalid or empty original chart data");const filteredData=filterChartDataByMarketplace(originalChartData,marketplace);if(!Array.isArray(filteredData)||filteredData.length===0)throw new Error("Filtering resulted in empty data set");chart.updateData(filteredData),updateResults.lastWeek=!0}catch{updateResults.lastWeek=!1}const monthlySalesChartContainer=document.querySelector("#monthly-sales-chart-container");if(monthlySalesChartContainer&&monthlySalesChartContainer.snapChart&&originalMonthlySalesData)try{const monthlyChart=monthlySalesChartContainer.snapChart;if(!Array.isArray(originalMonthlySalesData)||originalMonthlySalesData.length===0)throw new Error("Invalid or empty original monthly sales data");const monthlyState=monthlySalesChartContainer.chartState||{},baselineData=Array.isArray(monthlyState.unfilteredData)&&monthlyState.unfilteredData.length>0?monthlyState.unfilteredData:originalMonthlySalesData,filteredMonthlyData=filterChartDataByMarketplace(baselineData,marketplace);if(!Array.isArray(filteredMonthlyData)||filteredMonthlyData.length===0)throw new Error("Monthly sales filtering resulted in empty data set");monthlyChart.updateData(filteredMonthlyData),updateResults.monthly=!0,scheduleQuarterlySummaryRecalc()}catch{updateResults.monthly=!1}else try{const chartContainer=document.querySelector("#monthly-sales-chart-container");if(chartContainer&&chartContainer.snapChart&&Array.isArray(originalMonthlySalesData)){const rebuilt=filterChartDataByMarketplace(originalMonthlySalesData,marketplace);Array.isArray(rebuilt)&&rebuilt.length>0&&(chartContainer.snapChart.updateData(rebuilt),scheduleQuarterlySummaryRecalc())}}catch{}const yearlySalesChartContainer=document.querySelector("#yearly-sales-chart-container");if(yearlySalesChartContainer&&yearlySalesChartContainer.snapChart&&originalYearlySalesData)try{const yearlyChart=yearlySalesChartContainer.snapChart;if(!Array.isArray(originalYearlySalesData)||originalYearlySalesData.length===0)throw new Error("Invalid or empty original yearly sales data");const filteredYearlyData=filterChartDataByMarketplace(originalYearlySalesData,marketplace);if(!Array.isArray(filteredYearlyData)||filteredYearlyData.length===0)throw new Error("Yearly sales filtering resulted in empty data set");yearlyChart.updateData(filteredYearlyData),updateResults.yearly=!0}catch{updateResults.yearly=!1}try{updateTodayVsPreviousYearsChartForMarketplace(marketplace),updateResults.todayVsPrevious=!0}catch{updateResults.todayVsPrevious=!1}const successCount=Object.values(updateResults).filter(Boolean).length,totalCharts=Object.keys(updateResults).length;return updateResults}function initializeLastWeekSalesButtons(){initializeCompareDropdown();const viewInsightsBtn=document.querySelector(".last-week-sales-card .view-insights-btn");viewInsightsBtn&&viewInsightsBtn.addEventListener("click",()=>{alert("View Insights functionality will be implemented soon!")}),document.querySelectorAll(".four-sales-cards-section .view-insights-btn").forEach((btn,index)=>{const cardTitles=["Current Month","Last Month","Current Quarter","Last Quarter"];btn.addEventListener("click",()=>{alert(`View Insights functionality for ${cardTitles[index]} will be implemented soon!`)})})}const GlobalDropdownManager={activeDropdown:null,dropdowns:new Map,subManagers:new Map,register(id,showFn,hideFn,category="general"){this.dropdowns.set(id,{show:showFn,hide:hideFn,category,isActive:!1})},registerSubManager(name,manager){this.subManagers.set(name,manager)},show(id){this.activeDropdown&&this.activeDropdown!==id&&this.hide(this.activeDropdown),this.subManagers.forEach((manager,name)=>{manager.hideAll&&typeof manager.hideAll=="function"&&manager.hideAll()});const dropdownInfo=this.dropdowns.get(id);dropdownInfo&&(dropdownInfo.show(),dropdownInfo.isActive=!0,this.activeDropdown=id)},hide(id){const dropdownInfo=this.dropdowns.get(id);dropdownInfo&&(dropdownInfo.hide(),dropdownInfo.isActive=!1,this.activeDropdown===id&&(this.activeDropdown=null))},hideAll(){this.dropdowns.forEach((info,id)=>{info.isActive&&(info.hide(),info.isActive=!1)}),this.subManagers.forEach((manager,name)=>{manager.hideAll&&typeof manager.hideAll=="function"&&manager.hideAll()}),this.activeDropdown=null},hasActiveDropdown(){return this.activeDropdown!==null}},MonthlySalesDropdownManager={activeDropdown:null,dropdowns:new Map,register(id,showFn,hideFn){this.dropdowns.set(id,{show:showFn,hide:hideFn})},show(id){if(GlobalDropdownManager.hideAll(),this.activeDropdown&&this.activeDropdown!==id){const activeDropdownFns=this.dropdowns.get(this.activeDropdown);activeDropdownFns&&activeDropdownFns.hide()}const dropdownFns=this.dropdowns.get(id);dropdownFns&&(dropdownFns.show(),this.activeDropdown=id)},hide(id){const dropdownFns=this.dropdowns.get(id);dropdownFns&&(dropdownFns.hide(),this.activeDropdown===id&&(this.activeDropdown=null))},hideAll(){this.dropdowns.forEach(fns=>{fns.hide()}),this.activeDropdown=null}};window.GlobalDropdownManager=GlobalDropdownManager,window.MonthlySalesDropdownManager=MonthlySalesDropdownManager,GlobalDropdownManager.registerSubManager("monthlySales",MonthlySalesDropdownManager);function initializeMonthlySalesButtons(){initializeMonthlySalesCompareDropdown(),initializeMonthlySalesYearDropdown()}function initializeMonthlySalesYearDropdown(){const dropdown=document.getElementById("monthlySalesYearDropdown");if(!dropdown)return;const header=dropdown.querySelector(".dropdown-header"),menu=dropdown.querySelector(".dropdown-menu"),dropdownList=dropdown.querySelector(".dropdown-list"),selectedLabel=dropdown.querySelector(".dropdown-selected-label");if(!header||!menu||!dropdownList||!selectedLabel)return;const currentYear=new Date().getFullYear(),startYear=2015,endYear=Math.max(currentYear,2025);let yearOptionsHTML="";yearOptionsHTML+=`
    <div class="dropdown-item" data-value="lifetime" style="display:flex;align-items:center;font-size:12px;padding:12px;cursor:pointer;">
      <span style="font-size:12px;vertical-align:middle;position:relative;top:1px;">Lifetime</span>
    </div>
  `;for(let year=endYear;year>=startYear;year--){const isSelected=year===currentYear;yearOptionsHTML+=`
      <div class="dropdown-item${isSelected?" selected":""}" data-value="${year}" style="display:flex;align-items:center;font-size:12px;padding:12px;cursor:pointer;">
        <span style="font-size:12px;vertical-align:middle;position:relative;top:1px;${isSelected?"font-weight:bold;color:#470CED;":""}">${year}</span>
      </div>
    `}dropdownList.innerHTML=yearOptionsHTML,selectedLabel.textContent=currentYear.toString();let isOpen=!1;function showDropdown(){dropdown.classList.add("focused"),menu.classList.remove("hidden"),header.setAttribute("aria-expanded","true"),isOpen=!0}function hideDropdown(){dropdown.classList.remove("focused"),menu.classList.add("hidden"),header.setAttribute("aria-expanded","false"),isOpen=!1}MonthlySalesDropdownManager.register("yearDropdown",showDropdown,hideDropdown),header.addEventListener("click",e=>{e.stopPropagation(),isOpen?MonthlySalesDropdownManager.hide("yearDropdown"):MonthlySalesDropdownManager.show("yearDropdown")});const yearItems=dropdownList.querySelectorAll(".dropdown-item");yearItems.forEach(item=>{item.addEventListener("click",e=>{e.stopPropagation();const selectedYear=item.getAttribute("data-value");yearItems.forEach(yearItem=>{yearItem.classList.remove("selected");const span2=yearItem.querySelector("span");span2.style.fontWeight="",span2.style.color=""}),item.classList.add("selected");const span=item.querySelector("span");if(span.style.fontWeight="bold",span.style.color="#470CED",selectedLabel.textContent=selectedYear==="lifetime"?"Lifetime":selectedYear,selectedYear==="lifetime"){updateMonthlySalesChartForLifetime(),updateMonthlySalesDateForLifetime(),hideMonthlySalesCompareButton();const qDiv=document.getElementById("quarter-sales-div");qDiv&&qDiv.classList.add("hidden")}else{updateMonthlySalesChartForYear(parseInt(selectedYear)),updateMonthlySalesDateForYear(parseInt(selectedYear)),showMonthlySalesCompareButton();const yearInt=parseInt(selectedYear);renderQuarterlySummary(yearInt)}MonthlySalesDropdownManager.hide("yearDropdown")})})}function updateMonthlySalesChartForLifetime(){const chartContainer=document.querySelector("#monthly-sales-chart-container");if(chartContainer){if(chartContainer.snapChart){try{chartContainer.snapChart.destroy()}catch{}chartContainer.snapChart=null}chartContainer.innerHTML="",chartContainer.chartState=null,window.SnapLoader&&window.SnapLoader.showOverlay(chartContainer,{text:"Loading lifetime data...",id:"monthly-sales-lifetime-loader"});try{const lifetimeData=generateMonthlySalesLifetimeData();originalMonthlySalesData=JSON.parse(JSON.stringify(lifetimeData));const currentMarketplaceFocus=globalMarketplaceFocus||"all";let filteredData=lifetimeData;currentMarketplaceFocus!=="all"&&(filteredData=filterChartDataByMarketplace(lifetimeData,currentMarketplaceFocus));const chartType=filteredData.length>12?"scrollable-stacked-column":"stacked-column";chartContainer.dataset.monthlyMode="lifetime",chartContainer.chartState={mode:"lifetime",selectedYear:null};const newChart=new SnapChart({container:chartContainer,type:chartType,data:filteredData,demoOptions:{showContainer:!1,showTitle:!1,showDataEditor:!1,showControls:!1,showInsights:!1},options:{responsive:!0,animate:!0,height:300,width:null,compareMode:!1,fullWidthDistribution:!0,preserveChartType:!0,xAxis:{type:"category",labels:{format:"MMM, 'YY"}}}});chartContainer.snapChart=newChart,chartContainer.chartState={mode:"lifetime",selectedYear:null,data:filteredData,unfilteredData:lifetimeData};const showReturns=sessionStorage.getItem("showReturns_monthly")==="true",showRoyalties=sessionStorage.getItem("showRoyalties_monthly")==="true";newChart.options.showReturns=showReturns,newChart.options.showRoyalties=showRoyalties,(!showReturns||!showRoyalties)&&newChart.render(),window.SnapLoader&&window.SnapLoader.hideOverlay(chartContainer)}catch{window.SnapLoader&&window.SnapLoader.hideOverlay(chartContainer)}}}function updateMonthlySalesChartForYear(selectedYear){const chartContainer=document.querySelector("#monthly-sales-chart-container");if(chartContainer){if(chartContainer.snapChart){try{chartContainer.snapChart.destroy()}catch{}chartContainer.snapChart=null}chartContainer.innerHTML="",chartContainer.chartState=null,window.SnapLoader&&window.SnapLoader.showOverlay(chartContainer,{text:"Loading year data...",id:"monthly-sales-year-loader"});try{const monthlyData=generateMonthlySalesDataForYear(selectedYear);originalMonthlySalesData=JSON.parse(JSON.stringify(monthlyData));const currentMarketplaceFocus=globalMarketplaceFocus||"all";let filteredData=monthlyData;currentMarketplaceFocus!=="all"&&(filteredData=filterChartDataByMarketplace(monthlyData,currentMarketplaceFocus));const chartType=filteredData.length>12?"scrollable-stacked-column":"stacked-column";chartContainer.dataset.monthlyMode="year",chartContainer.chartState={mode:"single-year",selectedYear};const newChart=new SnapChart({container:chartContainer,type:chartType,data:filteredData,demoOptions:{showContainer:!1,showTitle:!1,showDataEditor:!1,showControls:!1,showInsights:!1},options:{responsive:!0,animate:!0,height:300,width:null,compareMode:!1,fullWidthDistribution:!0,preserveChartType:!0,xAxis:{type:"category",labels:{format:"MMM, 'YY"}}}});chartContainer.snapChart=newChart,chartContainer.chartState={mode:"single-year",selectedYear,data:filteredData,unfilteredData:monthlyData};try{renderQuarterlySummary(selectedYear),attachQuarterlySummaryHook(chartContainer)}catch{}const compareBtn=document.querySelector(".monthly-sales-card .compare-btn");if(compareBtn&&compareBtn.classList.contains("active")){const selectedCompareItem=document.querySelector(".monthly-sales-card .compare-dropdown-item.selected"),currentCompareValue=selectedCompareItem?selectedCompareItem.getAttribute("data-value"):"previous-year",comparisonData=generateMonthlySalesComparisonData(currentCompareValue);let filteredComparisonData=comparisonData;currentMarketplaceFocus!=="all"&&(filteredComparisonData=filterChartDataByMarketplace(comparisonData,currentMarketplaceFocus)),newChart.options.compareMode=!0,newChart.options.compareData=filteredComparisonData,newChart.render()}const showReturns=sessionStorage.getItem("showReturns_monthly")==="true",showRoyalties=sessionStorage.getItem("showRoyalties_monthly")==="true";newChart.options.showReturns=showReturns,newChart.options.showRoyalties=showRoyalties,(!showReturns||!showRoyalties)&&newChart.render()}catch{}finally{window.SnapLoader&&window.SnapLoader.hideOverlay(chartContainer)}}}function generateMonthlySalesDataForYear(year){const monthNames=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],today=window.SnapTimezone.getPacificTime(),currentYear=today.getFullYear(),currentMonth=today.getMonth(),monthlyData=[],monthsToShow=year===currentYear?currentMonth+1:12;function generateRandomMonthSales(monthIndex){const allMarketplaces=["US","UK","DE","FR","IT","ES","JP"],seasonalMultiplier=[.8,.9,1.1,1.2,1,.9,.8,.9,1.1,1.3,1.4,1.2][monthIndex]||1,numActiveMarketplaces=Math.floor(Math.random()*4)+4,activeMarketplaces=[...allMarketplaces].sort(()=>.5-Math.random()).slice(0,numActiveMarketplaces),salesData={};return allMarketplaces.forEach(marketplace=>{if(activeMarketplaces.includes(marketplace)){let baseSales;marketplace==="US"?baseSales=Math.floor((Math.random()*800+200)*seasonalMultiplier):marketplace==="UK"||marketplace==="DE"?baseSales=Math.floor((Math.random()*400+100)*seasonalMultiplier):baseSales=Math.floor((Math.random()*200+25)*seasonalMultiplier);const randomVariation=.8+Math.random()*.4;baseSales=Math.round(baseSales*randomVariation),salesData[marketplace]={sales:Math.max(0,baseSales),returns:Math.random()<.7?0:Math.floor(Math.random()*Math.max(1,baseSales*.12))}}else salesData[marketplace]={sales:0,returns:0}}),salesData}for(let month=0;month<monthsToShow;month++){let monthData,totalSales=0,totalReturns=0;monthData=generateRandomMonthSales(month),totalSales=Object.values(monthData).reduce((sum,data)=>sum+data.sales,0),totalReturns=Object.values(monthData).reduce((sum,data)=>sum+data.returns,0);const marketplaces=Object.entries(monthData).map(([code,data])=>({code,sales:data.sales,royalties:data.sales>0?Math.floor(data.sales*(Math.random()*3+2.5)):0,returns:data.returns})),monthDate=new Date(year,month,1),monthName=monthNames[month],yearAbbreviation=year.toString().slice(-2),formattedLabel=`${monthName}, '${yearAbbreviation}`;monthlyData.push({label:formattedLabel,sales:totalSales,returns:totalReturns,marketplaces,month:monthName.toUpperCase(),year:yearAbbreviation,day:"01",isMonthlyData:!0,dateObj:monthDate,values:marketplaces.map(mp=>mp.sales),labels:marketplaces.map(mp=>mp.code)})}return monthlyData}function generateMonthlySalesLifetimeData(){const today=window.SnapTimezone.getPacificTime(),currentYear=today.getFullYear(),currentMonth=today.getMonth(),startYear=2015,lifetimeData=[];for(let year=startYear;year<=currentYear;year++){const monthsToShow=year===currentYear?currentMonth+1:12,yearData=generateMonthlySalesDataForYear(year);lifetimeData.push(...yearData)}return lifetimeData}function renderQuarterlySummary(year){const qDiv=document.getElementById("quarter-sales-div");if(!qDiv)return;qDiv.classList.remove("hidden");const yearInt=parseInt(year);let monthly=[];const mContainer=document.querySelector("#monthly-sales-chart-container"),chartData=mContainer&&mContainer.snapChart?mContainer.snapChart.data:null;if(Array.isArray(chartData)&&chartData.length>0){const monthsArr=new Array(12).fill(void 0);chartData.forEach(d=>{if(!d)return;const dDate=d.dateObj?new Date(d.dateObj):null,dYear=dDate?dDate.getFullYear():null;if(dDate&&dYear===yearInt){const mIdx=dDate.getMonth();monthsArr[mIdx]=d}}),monthly=monthsArr}else monthly=generateMonthlySalesDataForYear(yearInt);const monthsByQuarter=[{label:"Q1",months:[0,1,2],lastMonth:2},{label:"Q2",months:[3,4,5],lastMonth:5},{label:"Q3",months:[6,7,8],lastMonth:8},{label:"Q4",months:[9,10,11],lastMonth:11}],today=window.SnapTimezone.getPacificTime(),isCurrentYear=today.getFullYear()===parseInt(year),completedMonthIndex=isCurrentYear?today.getMonth():11,prevYear=parseInt(year)-1;let monthlyPrev=[];if(Array.isArray(chartData)&&chartData.length>0){const monthsArrPrev=new Array(12).fill(void 0);chartData.forEach(d=>{if(!d)return;const dDate=d.dateObj?new Date(d.dateObj):null,dYear=dDate?dDate.getFullYear():null;if(dDate&&dYear===prevYear){const mIdx=dDate.getMonth();monthsArrPrev[mIdx]=d}}),monthlyPrev=monthsArrPrev.some(v=>!!v)?monthsArrPrev:generateMonthlySalesDataForYear(prevYear)}else monthlyPrev=generateMonthlySalesDataForYear(prevYear);const quarterCards=monthsByQuarter.map(({label,months,lastMonth})=>{if(!(months.every(m=>monthly[m])&&(!isCurrentYear||completedMonthIndex>=lastMonth)))return null;const slice=months.map(m=>monthly[m]).filter(Boolean),totalSales=slice.reduce((s,d)=>s+(d?.sales||0),0),totalReturns=slice.reduce((s,d)=>s+(d?.returns||0),0),totalRoyalties=slice.reduce((s,d)=>s+(Array.isArray(d?.marketplaces)?d.marketplaces.reduce((rs,mp)=>rs+(mp.royalties||0),0):0),0),returnRate=totalSales>0?totalReturns/totalSales*100:0,prevSales=months.map(m=>monthlyPrev[m]).filter(Boolean).reduce((s,d)=>s+(d?.sales||0),0);let comparePercent=null,compareClass="neutral",compareIcon="./assets/up-per-ic.svg";if(prevSales>0){const pct=(totalSales-prevSales)/prevSales*100;comparePercent=`${pct>=0?"+":""}${pct.toFixed(1)}%`,compareClass=pct<0?"negative":pct>0?"positive":"neutral",compareIcon=pct<0?"./assets/down-per-ic.svg":"./assets/up-per-ic.svg"}else prevSales===0&&(totalSales>0?(comparePercent="+100%",compareClass="positive",compareIcon="./assets/up-per-ic.svg"):(comparePercent="0.0%",compareClass="neutral",compareIcon="./assets.up-per-ic.svg"));return{label,sales:totalSales,royalties:totalRoyalties,returns:totalReturns,rate:`${returnRate.toFixed(1)}%`,compare:{percent:comparePercent,className:compareClass,icon:compareIcon}}}).filter(Boolean),html=`
    <div class="quarter-grid">
      ${quarterCards.map((q,index)=>{const returnsIsDash=q.returns==="-",rateIsZeroOrDash=q.rate==="-"||q.rate==="0.0%",returnsZeroClass=returnsIsDash||q.returns===0?"zero":"",rateClass=rateIsZeroOrDash||q.returns===0?"zero":"",salesZeroClass=q.sales===0||q.sales==="-"?"zero":"",royaltiesClass=q.royalties==="-"?"zero":q.royalties<0?"negative":q.royalties>0?"positive":"zero",returnsAnalyticsCell=returnsIsDash?"-":`<span class="metric-value returned ${returnsZeroClass}">${formatNumberOrDash(q.returns)}</span> <span class="metric-percentage returned-percentage ${rateClass}">${q.rate}</span>`,quarterCard=`
          <div class="quarter-card">
            <div class="quarter-label">${q.label}
              ${q.compare?`<span class="quarter-compare" data-tooltip="Compared to ${q.label} last year" aria-label="Compared to ${q.label} last year">
                <img src="${q.compare.icon}" alt="${q.compare.className==="negative"?"Down":"Up"}" class="comparison-arrow" width="10" height="6" />
                <span class="comparison-percentage ${q.compare.className}">${q.compare.percent}</span>
              </span>`:""}
            </div>
            <div class="quarter-table">
              <div class="quarter-row header">
                <div class="quarter-cell header-cell">Sales</div>
                <div class="quarter-cell header-cell">Royalties</div>
                <div class="quarter-cell header-cell">Returns</div>
              </div>
              <div class="quarter-row values">
                <div class="quarter-cell value-cell">
                  <span class="metric-value sales ${salesZeroClass}">${formatNumberOrDash(q.sales)}</span>
                </div>
                <div class="quarter-cell value-cell">
                  <span class="metric-value royalties ${royaltiesClass}">${formatCurrencyOrDash(q.royalties)}</span>
                </div>
                <div class="quarter-cell value-cell">
                  ${returnsAnalyticsCell}
                </div>
              </div>
            </div>
          </div>
        `,divider=index<quarterCards.length-1?'<div class="quarter-divider"></div>':"";return quarterCard+divider}).join("")}
    </div>
  `;qDiv.innerHTML=html}function scheduleQuarterlySummaryRecalc(){try{const chartContainer=document.querySelector("#monthly-sales-chart-container");if(!chartContainer||!chartContainer.snapChart)return;const state=chartContainer.chartState||{};if(state.mode!=="single-year"||!state.selectedYear)return;requestAnimationFrame(()=>{setTimeout(()=>{try{renderQuarterlySummary(state.selectedYear)}catch{}},0)})}catch{}}function formatNumberOrDash(v){if(v==="-")return"-";try{return Number(v).toLocaleString("en-US")}catch{return String(v)}}function formatCurrencyOrDash(v){if(v==="-")return"-";try{return`$${Number(v).toLocaleString("en-US")}`}catch{return String(v)}}function attachQuarterlySummaryHook(chartContainer){if(!chartContainer||!chartContainer.snapChart)return;const chart=chartContainer.snapChart;if(chart._renderWrappedForQuarterly)return;const originalRender=chart.render&&chart.render.bind(chart);typeof originalRender=="function"&&(chart.render=function(...args){const result=originalRender(...args);try{const state=chartContainer.chartState||{};state.mode==="single-year"&&state.selectedYear&&renderQuarterlySummary(state.selectedYear)}catch{}return result},chart._renderWrappedForQuarterly=!0)}function updateMonthlySalesDateForLifetime(){const dateElement=document.getElementById("monthly-sales-date");if(dateElement){const formattedDate=`2015 to ${window.SnapTimezone.getPacificTime().getFullYear()}`;dateElement.textContent=formattedDate}}function updateMonthlySalesDateForYear(selectedYear){const dateElement=document.getElementById("monthly-sales-date");if(dateElement){const today=window.SnapTimezone.getPacificTime(),currentYear=today.getFullYear();let formattedDate;if(selectedYear===currentYear){const currentMonthName=today.toLocaleDateString("en-US",{month:"long"});formattedDate=`January ${selectedYear} to ${currentMonthName} ${selectedYear}`}else formattedDate=`January ${selectedYear} to December ${selectedYear}`;dateElement.textContent=formattedDate}}function hideMonthlySalesCompareButton(){const compareDiv=document.querySelector(".monthly-sales-card .compare-div");compareDiv&&(compareDiv.style.display="none");const qDiv=document.getElementById("quarter-sales-div");qDiv&&qDiv.classList.add("hidden")}function showMonthlySalesCompareButton(){const compareDiv=document.querySelector(".monthly-sales-card .compare-div");compareDiv&&(compareDiv.style.display="flex")}function generateYearlySalesData(){const currentYear=window.SnapTimezone.getPacificTime().getFullYear(),startYear=2e3,endYear=currentYear,totalYears=endYear-startYear+1,yearlyData=[];function generateYearSalesData(year){const allMarketplaces=["US","UK","DE","FR","IT","ES","JP"],baseGrowthMultiplier=1+(year-startYear)*.15,randomVariation=.8+Math.random()*.4,finalMultiplier=baseGrowthMultiplier*randomVariation,salesData={};return allMarketplaces.forEach(marketplace=>{let baseSales;marketplace==="US"?baseSales=Math.floor((Math.random()*8e3+3e3)*finalMultiplier):marketplace==="UK"||marketplace==="DE"?baseSales=Math.floor((Math.random()*4e3+1500)*finalMultiplier):baseSales=Math.floor((Math.random()*2e3+500)*finalMultiplier),salesData[marketplace]={sales:Math.max(0,baseSales),returns:Math.random()<.6?Math.floor(Math.random()*Math.max(1,baseSales*.08)):0}}),salesData}for(let year=startYear;year<=endYear;year++){const yearData=generateYearSalesData(year),totalSales=Object.values(yearData).reduce((sum,data)=>sum+data.sales,0),totalReturns=Object.values(yearData).reduce((sum,data)=>sum+data.returns,0),marketplaces=Object.entries(yearData).map(([code,data])=>({code,sales:data.sales,royalties:data.sales>0?Math.floor(data.sales*(Math.random()*3+2.5)):0,returns:data.returns})),yearDate=new Date(year,0,1),formattedLabel=year.toString();yearlyData.push({label:formattedLabel,sales:totalSales,returns:totalReturns,marketplaces,month:"JAN",year:year.toString().slice(-2),day:"01",isYearlyData:!0,dateObj:yearDate,values:marketplaces.map(mp=>mp.sales),labels:marketplaces.map(mp=>mp.code)})}return yearlyData}async function generateYearlySalesDataAsync(){return new Promise(resolve=>{setTimeout(()=>{try{const data=generateYearlySalesData();resolve(data)}catch{resolve([])}},0)})}async function initializeYearlySalesChart(){const chartContainer=document.querySelector("#yearly-sales-chart-container");if(!chartContainer)return;window.SnapLoader&&window.SnapLoader.showOverlay(chartContainer,{text:"Loading chart data...",id:"yearly-sales-chart-loader"}),chartContainer.style.borderRadius="14px",window.SnapLoader&&window.SnapLoader.showOverlay(chartContainer,{text:"Generating yearly sales data...",id:"yearly-sales-data-loader"});const yearlyData=await generateYearlySalesDataAsync();originalYearlySalesData=JSON.parse(JSON.stringify(yearlyData));const chartType=yearlyData.length>12?"scrollable-stacked-column":"stacked-column";try{const chart=new SnapChart({container:chartContainer,type:chartType,data:yearlyData,demoOptions:{showContainer:!1,showTitle:!1,showDataEditor:!1,showControls:!1,showInsights:!1},options:{responsive:!0,animate:!0,height:300,width:null,fullWidthDistribution:!0,compareMode:!1,xAxis:{showLabels:!0,labelFormat:"year"}}});chartContainer.snapChart=chart;try{const showReturns=sessionStorage.getItem("showReturns_yearly")==="true",showRoyalties=sessionStorage.getItem("showRoyalties_yearly")==="true";chart.options.showReturns=showReturns,chart.options.showRoyalties=showRoyalties,(!showReturns||!showRoyalties)&&chart.render()}catch{}window.SnapLoader&&(window.SnapLoader.hideOverlay(chartContainer),window.SnapLoader.hideOverlay("yearly-sales-data-loader"))}catch{window.SnapLoader&&window.SnapLoader.hideOverlay(chartContainer)}}let globalCompareSelection="none",globalCompareDropdownElements=null;function resetCompareMode(){globalCompareDropdownElements?updateCompareSelection("none"):globalCompareSelection="none",updateMonthlySalesCompareSelection("none")}function updateCompareSelection(value){if(globalCompareSelection=value,!globalCompareDropdownElements)return;const{compareBtn,dropdownItems}=globalCompareDropdownElements;dropdownItems.forEach(item=>{const checkboxImg=item.querySelector(".compare-checkbox img");item.getAttribute("data-value")===value?(item.classList.add("selected"),checkboxImg.src="./assets/checkbox-ic.svg"):(item.classList.remove("selected"),checkboxImg.src="./assets/uncheckedbox-ic.svg")}),value==="none"?compareBtn.classList.remove("active"):compareBtn.classList.add("active");const chartContainer=document.querySelector("#last-week-chart-container");chartContainer&&window.SnapLoader&&window.SnapLoader.showOverlay(chartContainer,{text:"Updating comparison...",id:"last-week-compare-loader"}),updateChartCompareMode(value),chartContainer&&window.SnapLoader&&window.SnapLoader.hideOverlay(chartContainer)}function initializeCompareDropdown(){const compareBtn=document.querySelector(".last-week-sales-card .compare-btn"),compareDropdown=document.querySelector(".last-week-sales-card .compare-dropdown"),dropdownItems=document.querySelectorAll(".last-week-sales-card .compare-dropdown-item");if(!compareBtn||!compareDropdown||dropdownItems.length===0)return;globalCompareDropdownElements={compareBtn,compareDropdown,dropdownItems};let dropdownVisible=!1;updateCompareSelection(globalCompareSelection),compareBtn.addEventListener("click",e=>{e.stopPropagation(),dropdownVisible?window.GlobalDropdownManager?GlobalDropdownManager.hide("lastWeekCompareDropdown"):hideDropdown():window.GlobalDropdownManager?GlobalDropdownManager.show("lastWeekCompareDropdown"):showDropdown()}),dropdownItems.forEach(item=>{item.addEventListener("click",e=>{e.stopPropagation();const value=item.getAttribute("data-value");updateCompareSelection(value),window.GlobalDropdownManager?GlobalDropdownManager.hide("lastWeekCompareDropdown"):hideDropdown()})});function handleClickOutside(e){!compareBtn.contains(e.target)&&!compareDropdown.contains(e.target)&&(window.GlobalDropdownManager?GlobalDropdownManager.hide("lastWeekCompareDropdown"):hideDropdown())}document.addEventListener("click",handleClickOutside);function showDropdown(){compareDropdown.classList.add("show"),compareBtn.classList.add("active"),dropdownVisible=!0}function hideDropdown(){compareDropdown.classList.remove("show"),compareBtn.classList.remove("active"),dropdownVisible=!1}window.GlobalDropdownManager&&GlobalDropdownManager.register("lastWeekCompareDropdown",showDropdown,hideDropdown,"compare")}function initializeMonthlySalesCompareDropdown(){const compareBtn=document.querySelector(".monthly-sales-card .compare-btn"),compareDropdown=document.querySelector(".monthly-sales-card .compare-dropdown"),dropdownItems=document.querySelectorAll(".monthly-sales-card .compare-dropdown-item");if(!compareBtn||!compareDropdown||dropdownItems.length===0)return;let dropdownVisible=!1;function showDropdown(){compareDropdown.classList.add("show"),compareBtn.classList.add("active"),dropdownVisible=!0}function hideDropdown(){compareDropdown.classList.remove("show"),compareBtn.classList.remove("active"),dropdownVisible=!1}MonthlySalesDropdownManager.register("compareDropdown",showDropdown,hideDropdown),updateMonthlySalesCompareSelection("none"),compareBtn.addEventListener("click",e=>{e.stopPropagation(),dropdownVisible?MonthlySalesDropdownManager.hide("compareDropdown"):MonthlySalesDropdownManager.show("compareDropdown")}),dropdownItems.forEach(item=>{item.addEventListener("click",e=>{e.stopPropagation();const value=item.getAttribute("data-value");updateMonthlySalesCompareSelection(value),MonthlySalesDropdownManager.hide("compareDropdown")})})}function updateMonthlySalesCompareSelection(value){const compareBtn=document.querySelector(".monthly-sales-card .compare-btn"),dropdownItems=document.querySelectorAll(".monthly-sales-card .compare-dropdown-item");!compareBtn||dropdownItems.length===0||(dropdownItems.forEach(item=>{const checkboxImg=item.querySelector(".compare-checkbox img");item.getAttribute("data-value")===value?(item.classList.add("selected"),checkboxImg.src="./assets/checkbox-ic.svg"):(item.classList.remove("selected"),checkboxImg.src="./assets/uncheckedbox-ic.svg")}),value==="none"?compareBtn.classList.remove("active"):compareBtn.classList.add("active"),updateMonthlySalesChartCompareMode(value))}function updateMonthlySalesChartCompareMode(compareValue){const chartContainer=document.querySelector("#monthly-sales-chart-container");if(!chartContainer||!chartContainer.snapChart||(chartContainer.dataset.monthlyMode||"year")==="lifetime")return;window.SnapLoader&&window.SnapLoader.showOverlay(chartContainer,{text:"Updating comparison...",id:"monthly-sales-compare-loader"});const chart=chartContainer.snapChart;try{if(compareValue==="none")chart.options.compareMode=!1,chart.options.compareData=null;else{chart.options.compareMode=!0;const comparisonData=generateMonthlySalesComparisonData(compareValue);chart.options.compareData=comparisonData}chart.render()}finally{window.SnapLoader&&window.SnapLoader.hideOverlay(chartContainer)}}function generateMonthlySalesComparisonData(compareValue){const chartContainer=document.querySelector("#monthly-sales-chart-container");if(!chartContainer||!chartContainer.snapChart)return null;const currentData=chartContainer.snapChart.data;return!currentData||currentData.length===0?null:compareValue==="previous-year"?generateMonthlySalesPreviousYearData(currentData):null}function generateMonthlySalesPreviousYearData(currentData){const currentYear=window.SnapTimezone.getPacificTime().getFullYear(),monthNames=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],mainDataYear=currentData&&currentData.length>0?parseInt("20"+currentData[0].year):currentYear,comparisonYear=mainDataYear-1;let monthsToGenerate;mainDataYear===currentYear?monthsToGenerate=currentData.length:monthsToGenerate=12;const previousYearData=[];function getRandomActiveMarketplaceCount(compareValue){switch(compareValue){case"week":return Math.floor(Math.random()*4)+3;case"month":return Math.floor(Math.random()*4)+2;case"previous-year":return Math.floor(Math.random()*5)+2;default:return Math.floor(Math.random()*4)+3}}for(let month=0;month<monthsToGenerate;month++){const monthName=monthNames[month],yearAbbreviation=comparisonYear.toString().slice(-2),formattedLabel=`${monthName}, '${yearAbbreviation}`,allMarketplaceCodes=["US","UK","DE","FR","IT","ES","JP"],numActiveMarketplaces=getRandomActiveMarketplaceCount("previous-year"),activeMarketplaces=[...allMarketplaceCodes].sort(()=>.5-Math.random()).slice(0,numActiveMarketplaces),marketplaces=[];let totalSales=0,totalRoyalties=0,totalReturns=0;allMarketplaceCodes.forEach(code=>{if(activeMarketplaces.includes(code)){const sales=Math.floor(Math.random()*800)+50,royalties=Math.round(sales*(Math.random()*8+2)*100)/100,returns=Math.floor(sales*(Math.random()*.15));marketplaces.push({code,sales,royalties,returns}),totalSales+=sales,totalRoyalties+=royalties,totalReturns+=returns}else marketplaces.push({code,sales:0,royalties:0,returns:0})});const comparisonMonthData={label:formattedLabel,sales:totalSales,royalties:totalRoyalties,returns:totalReturns,marketplaces,month:monthName.toUpperCase(),year:yearAbbreviation,day:"01",isMonthlyData:!0,dateObj:new Date(comparisonYear,month,1),values:marketplaces.map(mp=>mp.sales),labels:marketplaces.map(mp=>mp.code)};previousYearData.push(comparisonMonthData)}return previousYearData}function updateChartCompareMode(compareValue){const chartContainer=document.querySelector("#last-week-chart-container");if(!chartContainer||!chartContainer.snapChart)return;const chart=chartContainer.snapChart;if(compareValue==="none")chart.options.compareMode=!1,chart.options.compareData=null;else{chart.options.compareMode=!0;const comparisonData=generateComparisonData(compareValue);chart.options.compareData=comparisonData}chart.render();function generateComparisonData(compareValue2){const chartContainer2=document.querySelector("#last-week-chart-container");if(!chartContainer2||!chartContainer2.snapChart)return null;const currentData=chartContainer2.snapChart.data;if(!currentData||currentData.length===0)return null;const comparisonPeriod=calculateComparisonPeriod(compareValue2);if(!comparisonPeriod)return null;const baseComparisonData=generateRealisticComparisonData(currentData,comparisonPeriod,compareValue2);return globalMarketplaceFocus&&globalMarketplaceFocus!=="all"?filterChartDataByMarketplace(baseComparisonData,globalMarketplaceFocus):baseComparisonData}function calculateComparisonPeriod(compareValue2){const now=new Date,pacificTime=new Date(now.toLocaleString("en-US",{timeZone:"America/Los_Angeles"})),today=new Date(pacificTime),currentEndDate=new Date(today);currentEndDate.setDate(today.getDate()-1);const currentStartDate=new Date(currentEndDate);currentStartDate.setDate(currentEndDate.getDate()-6);let comparisonStartDate,comparisonEndDate;switch(compareValue2){case"week":comparisonEndDate=new Date(currentStartDate),comparisonEndDate.setDate(currentStartDate.getDate()-1),comparisonStartDate=new Date(comparisonEndDate),comparisonStartDate.setDate(comparisonEndDate.getDate()-6);break;case"month":comparisonStartDate=new Date(currentStartDate),comparisonStartDate.setMonth(currentStartDate.getMonth()-1),comparisonEndDate=new Date(currentEndDate),comparisonEndDate.setMonth(currentEndDate.getMonth()-1);break;case"year":comparisonStartDate=new Date(currentStartDate),comparisonStartDate.setFullYear(currentStartDate.getFullYear()-1),comparisonEndDate=new Date(currentEndDate),comparisonEndDate.setFullYear(currentEndDate.getFullYear()-1);break;default:return null}return{startDate:comparisonStartDate,endDate:comparisonEndDate,currentStartDate,currentEndDate}}function generateRealisticComparisonData(currentData,comparisonPeriod,compareValue2){const comparisonData=[];for(let i=0;i<currentData.length;i++){const currentDayData=currentData[i],comparisonDate=new Date(comparisonPeriod.startDate);comparisonDate.setDate(comparisonPeriod.startDate.getDate()+i);const comparisonMarketplaces=generateIndependentMarketplaceActivity(currentDayData.marketplaces,compareValue2),comparisonDayData={month:comparisonDate.toLocaleDateString("en-US",{month:"short",timeZone:"America/Los_Angeles"}).toUpperCase(),day:comparisonDate.getDate().toString().padStart(2,"0"),year:comparisonDate.getFullYear().toString().slice(-2),marketplaces:comparisonMarketplaces,sales:comparisonMarketplaces.reduce((sum,mp)=>sum+mp.sales,0),royalties:comparisonMarketplaces.reduce((sum,mp)=>sum+mp.royalties,0),returns:comparisonMarketplaces.reduce((sum,mp)=>sum+mp.returns,0),values:comparisonMarketplaces.map(mp=>mp.sales),labels:comparisonMarketplaces.map(mp=>mp.code)};comparisonData.push(comparisonDayData)}return comparisonData}function generateIndependentMarketplaceActivity(currentMarketplaces,compareValue2){const allMarketplaceCodes=["US","UK","DE","FR","IT","ES","JP"],numActiveMarketplaces=getRandomActiveMarketplaceCount(compareValue2),activeMarketplaceCodes=[...allMarketplaceCodes].sort(()=>.5-Math.random()).slice(0,numActiveMarketplaces);return currentMarketplaces.map(currentMp=>{if(activeMarketplaceCodes.includes(currentMp.code)){const variationMultiplier=getRealisticVariationMultiplier(compareValue2),baseSales=Math.floor(Math.random()*90)+10,comparisonSales=Math.max(0,Math.round(baseSales*variationMultiplier)),royaltyRate=.1+Math.random()*.15,comparisonRoyalties=Math.round(comparisonSales*royaltyRate);let comparisonReturns=0;if(comparisonSales>0){const returnRate=Math.random()<.7?0:Math.random()*.1;comparisonReturns=Math.round(comparisonSales*returnRate)}return{...currentMp,sales:comparisonSales,royalties:comparisonRoyalties,returns:comparisonReturns}}else return{...currentMp,sales:0,royalties:0,returns:0}})}function getRandomActiveMarketplaceCount(compareValue2){switch(compareValue2){case"week":return Math.floor(Math.random()*4)+3;case"month":return Math.floor(Math.random()*6)+2;case"year":return Math.floor(Math.random()*7)+1;default:return 4}}function getRealisticVariationMultiplier(compareValue2){switch(compareValue2){case"week":return .8+Math.random()*.4;case"month":return .6+Math.random()*.8;case"year":return .4+Math.random()*1.2;default:return 1}}}function setupRealTimeColorMonitoring(){const observer=new MutationObserver(mutations=>{const listingsToUpdate=new Set;mutations.forEach(mutation=>{if(mutation.type==="childList"||mutation.type==="attributes"){const checkNode=node=>{if(node.nodeType===Node.ELEMENT_NODE){const orderedColorsBadge=node.closest?.(".ordered-colors-badge")||node.querySelector?.(".ordered-colors-badge");if(orderedColorsBadge){const listingDiv=orderedColorsBadge.closest(".listing-analytics-div");listingDiv&&listingsToUpdate.add(listingDiv)}node.classList?.contains("listing-analytics-div")&&listingsToUpdate.add(node)}};checkNode(mutation.target),mutation.addedNodes.forEach(checkNode),mutation.removedNodes.forEach(checkNode)}}),listingsToUpdate.forEach(listing=>{updateListingProductImageBackground(listing)}),listingsToUpdate.size>0});document.querySelectorAll(".todays-sales-card-div, .yesterdays-sales-card-div, .current-month-card-div, .last-month-card-div, .current-year-card-div, .last-year-card-div").forEach(salesCard=>{observer.observe(salesCard,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["style","class"],characterData:!0})}),window.colorMonitoringObserver=observer}function triggerBackgroundColorUpdate(specificListing=null){specificListing?updateListingProductImageBackground(specificListing):document.querySelectorAll(".listing-analytics-div").forEach(listing=>{updateListingProductImageBackground(listing)})}function stopRealTimeColorMonitoring(){window.colorMonitoringObserver&&(window.colorMonitoringObserver.disconnect(),window.colorMonitoringObserver=null)}function updateListingProductImageBackground(listingElement){const productImg=listingElement.querySelector(".listing-product-img"),orderedColorsBadge=listingElement.querySelector(".ordered-colors-badge");if(!productImg)return;let backgroundColor="#000000";if(orderedColorsBadge){const firstColorItem=orderedColorsBadge.querySelector(".color-item");if(firstColorItem){const firstColorCircle=firstColorItem.querySelector(".color-circle");if(firstColorCircle){const style=firstColorCircle.getAttribute("style");if(style){const colorMatch=style.match(/background-color:\s*([^;]+)/);colorMatch&&colorMatch[1]&&(backgroundColor=colorMatch[1].trim())}}}}const isWhiteColor=backgroundColor.includes("255, 255, 255")||backgroundColor==="#FFFFFF"||backgroundColor==="#ffffff"||backgroundColor==="rgb(255, 255, 255)"||backgroundColor==="white",isLightMode=document.documentElement.getAttribute("data-theme")!=="dark";productImg.style.backgroundColor=backgroundColor,isWhiteColor&&isLightMode?productImg.style.border="1px solid rgba(96, 111, 149, 0.15)":productImg.style.border="none";const listingTitle=listingElement.querySelector(".listing-title")?.textContent||"Unknown",borderInfo=isWhiteColor&&isLightMode?" with light gray border":""}function initializeListingIconTooltips(){if(document.querySelectorAll(".listing-icon-tooltip, .listing-icon-tooltip-arrow").forEach(el=>el.remove()),[{selector:".listing-analyse-ic",tooltipText:"Analyse Listing"},{selector:".listing-edit-ic",tooltipText:"Edit Listing"}].forEach(({selector,tooltipText})=>{document.querySelectorAll(selector).forEach(iconElement=>{const tooltip=document.createElement("div");tooltip.className="listing-icon-tooltip",tooltip.textContent=tooltipText,tooltip.style.cssText=`
        position: fixed;
        padding: 8px 16px;
        background: #000000;
        color: #FFFFFF;
        font-family: 'Amazon Ember', sans-serif;
        font-size: 12px;
        font-weight: 500;
        border-radius: 6px;
        white-space: nowrap;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.2s ease, visibility 0.2s ease;
        z-index: 10000000;
        pointer-events: none;
        width: auto;
        min-width: fit-content;
        max-width: none;
        text-align: center;
      `;const arrow=document.createElement("div");arrow.className="listing-icon-tooltip-arrow",arrow.style.cssText=`
        position: fixed;
        width: 0;
        height: 0;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid #000000;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.2s ease, visibility 0.2s ease;
        z-index: 10000000;
        pointer-events: none;
      `,document.body.appendChild(tooltip),document.body.appendChild(arrow);function showTooltip(){const rect=iconElement.getBoundingClientRect(),tooltipRect=tooltip.getBoundingClientRect(),left=rect.left+rect.width/2-tooltipRect.width/2,top=rect.top-tooltipRect.height-8,finalLeft=Math.max(10,Math.min(left,window.innerWidth-tooltipRect.width-10)),finalTop=Math.max(10,top);tooltip.style.left=`${finalLeft}px`,tooltip.style.top=`${finalTop}px`,tooltip.style.opacity="1",tooltip.style.visibility="visible",arrow.style.left=`${rect.left+rect.width/2-5}px`,arrow.style.top=`${finalTop+tooltipRect.height}px`,arrow.style.opacity="1",arrow.style.visibility="visible",activeCustomTooltips.set(iconElement,{isVisible:!0,hideFunction:hideTooltip,showFunction:showTooltip})}function hideTooltip(){tooltip.style.opacity="0",tooltip.style.visibility="hidden",arrow.style.opacity="0",arrow.style.visibility="hidden",activeCustomTooltips.delete(iconElement)}iconElement.addEventListener("mouseenter",showTooltip),iconElement.addEventListener("mouseleave",hideTooltip),iconElement._customTooltip=tooltip,iconElement._customArrow=arrow})}),!document.getElementById("listing-icon-tooltip-style")){const style=document.createElement("style");style.id="listing-icon-tooltip-style",style.textContent=`
      [data-theme="dark"] .listing-icon-tooltip {
        background: #000000 !important;
        color: #FFFFFF !important;
      }
      [data-theme="dark"] .listing-icon-tooltip-arrow {
        border-top-color: #000000 !important;
      }
    `,document.head.appendChild(style)}}function getPrivacyModeHTML(){return`
    <div class="privacy-mode">
      <div class="privacy-mode-label">
        <span>Privacy</span>
      </div>
      <div class="privacy-mode-toggle">
        <div class="off-tab ${isPrivacyModeEnabled()?"":"active"}" data-mode="off" data-tooltip="Display all data">
          <div class="off-div">
            <img src="./assets/${isPrivacyModeEnabled()?"privacy-off-inative.svg":"privacy-off-ative.svg"}" alt="Privacy Off" class="privacy-icon" width="12" height="12" />
            <span>Off</span>
          </div>
        </div>
        <div class="on-tab ${isPrivacyModeEnabled()?"active":""}" data-mode="on" data-tooltip="Hide all sensitive data">
          <div class="on-div">
            <img src="./assets/${isPrivacyModeEnabled()?"privacy-on-ative.svg":"privacy-on-inative.svg"}" alt="Privacy On" class="privacy-icon" width="12" height="12" />
            <span>On</span>
          </div>
        </div>
      </div>
    </div>
  `}function isPrivacyModeEnabled(){return localStorage.getItem("privacyMode")==="enabled"}function togglePrivacyMode(enable){enable?(localStorage.setItem("privacyMode","enabled"),document.body.classList.add("privacy-mode-enabled")):(localStorage.setItem("privacyMode","disabled"),document.body.classList.remove("privacy-mode-enabled")),updatePrivacyModeUI()}function updatePrivacyModeUI(){const isEnabled=isPrivacyModeEnabled(),offTab=document.querySelector(".privacy-mode-toggle .off-tab"),onTab=document.querySelector(".privacy-mode-toggle .on-tab");if(offTab){const offIcon=offTab.querySelector(".privacy-icon");offIcon&&(offIcon.src=`./assets/${isEnabled?"privacy-off-inative.svg":"privacy-off-ative.svg"}`),isEnabled?offTab.classList.remove("active"):offTab.classList.add("active")}if(onTab){const onIcon=onTab.querySelector(".privacy-icon");onIcon&&(onIcon.src=`./assets/${isEnabled?"privacy-on-ative.svg":"privacy-on-inative.svg"}`),isEnabled?onTab.classList.add("active"):onTab.classList.remove("active")}document.querySelectorAll(".listing-analytics-div").forEach(listingDiv=>{const imageContainer=listingDiv.querySelector(".listing-left-div");imageContainer&&(isEnabled?(imageContainer.classList.add("state-private"),imageContainer.classList.remove("state-loaded")):imageContainer.classList.contains("state-private")&&(imageContainer.classList.remove("state-private"),imageContainer.classList.add("state-loaded")))})}function setupPrivacyMode(){const privacyMount=document.getElementById("dashboard-privacy-mode-mount");if(!privacyMount)return;localStorage.getItem("privacyMode")===null&&localStorage.setItem("privacyMode","disabled"),isPrivacyModeEnabled()?document.body.classList.add("privacy-mode-enabled"):document.body.classList.remove("privacy-mode-enabled"),privacyMount.innerHTML=getPrivacyModeHTML();const offTab=privacyMount.querySelector(".off-tab"),onTab=privacyMount.querySelector(".on-tab");offTab&&offTab.addEventListener("click",()=>{togglePrivacyMode(!1)}),onTab&&onTab.addEventListener("click",()=>{togglePrivacyMode(!0)}),updatePrivacyModeUI()}function updateContainerStylesForTheme(){const accountStatus=document.querySelector(".account-status"),adSpend=document.querySelector(".ad-spend");if(document.querySelector(".account-ad-spend-wrapper")&&accountStatus&&adSpend){const currentAccountStyles=accountStatus.style.cssText;(currentAccountStyles.includes("background:")||currentAccountStyles.includes("border:"))&&(accountStatus.style.cssText=`
        flex: 0 0 calc(70% - 11.2px);
        margin-top: 0;
        width: auto;
        min-width: 0 !important;
        min-height: 92px;
        background: var(--bg-primary);
        border-radius: 14px;
        padding: 24px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
      `);const currentAdSpendStyles=adSpend.style.cssText;(currentAdSpendStyles.includes("background:")||currentAdSpendStyles.includes("border:"))&&(adSpend.style.cssText=`
        flex: 0 0 calc(30% - 4.8px);
        margin-top: 0;
        width: auto;
        min-width: 0 !important;
        background: var(--bg-primary);
        border-radius: 14px;
        padding: 24px !important;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        gap: 18px;
      `);const customDivider=document.querySelector(".ad-spend-single-marketplace-divider");customDivider&&customDivider.style.background&&(customDivider.style.background="var(--text-primary)");const headerDivider=document.querySelector(".ad-spend-header-divider");headerDivider&&headerDivider.style.background&&(headerDivider.style.background="var(--text-primary)")}}function initializeThemeChangeListener(){new MutationObserver(mutations=>{mutations.forEach(mutation=>{if(mutation.type==="attributes"&&mutation.attributeName==="data-theme"){updateContainerStylesForThemeOptimized();const listingAnalytics=document.querySelectorAll(".listing-analytics-div");updateListingProductImageBackgroundsOptimized(listingAnalytics)}})}).observe(document.documentElement,{attributes:!0,attributeFilter:["data-theme"]})}function updateSalesCardPadding(salesCard){const scrollableContent=salesCard.querySelector(".sales-scrollable-content");if(!scrollableContent)return;scrollableContent.scrollHeight>scrollableContent.clientHeight?(salesCard.classList.add("scrollbar-visible"),salesCard.classList.remove("scrollbar-hidden")):(salesCard.classList.add("scrollbar-hidden"),salesCard.classList.remove("scrollbar-visible"))}function updateAllSalesCardsPadding(){document.querySelectorAll(".sales-cards-container .sales-cards-row .todays-sales-card-div, .sales-cards-container .sales-cards-row .yesterdays-sales-card-div").forEach((salesCard,index)=>{updateSalesCardPadding(salesCard)})}function initDynamicSalesCardPadding(){updateAllSalesCardsPadding(),window.addEventListener("resize",()=>{setTimeout(updateAllSalesCardsPadding,100)});const salesCardsContainer=document.querySelector(".sales-cards-container");salesCardsContainer&&new MutationObserver(mutations=>{let shouldUpdate=!1;mutations.forEach(mutation=>{(mutation.target.classList.contains("sales-scrollable-content")||mutation.target.closest(".sales-scrollable-content"))&&(shouldUpdate=!0)}),shouldUpdate&&setTimeout(updateAllSalesCardsPadding,50)}).observe(salesCardsContainer,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["style","class"]}),window.updateSalesCardsPaddingAfterDataLoad=updateAllSalesCardsPadding}window.updateSalesCardPadding=updateSalesCardPadding,window.updateAllSalesCardsPadding=updateAllSalesCardsPadding,window.initDynamicSalesCardPadding=initDynamicSalesCardPadding;async function updateContainerStylesForThemeOptimized(){const accountStatus=document.querySelector(".account-status"),adSpend=document.querySelector(".ad-spend");if(document.querySelector(".account-ad-spend-wrapper")&&accountStatus&&adSpend){const operations=[],currentAccountStyles=accountStatus.style.cssText;(currentAccountStyles.includes("background:")||currentAccountStyles.includes("border:"))&&operations.push({type:"style",element:accountStatus,styles:{flex:"0 0 calc(70% - 11.2px)",marginTop:"0",width:"auto",minWidth:"0 !important",minHeight:"92px",background:"var(--bg-primary)",borderRadius:"14px",padding:"24px",boxSizing:"border-box",display:"flex",flexDirection:"column"}});const currentAdSpendStyles=adSpend.style.cssText;(currentAdSpendStyles.includes("background:")||currentAdSpendStyles.includes("border:"))&&operations.push({type:"style",element:adSpend,styles:{flex:"0 0 calc(30% - 4.8px)",marginTop:"0",width:"auto",minWidth:"0 !important",background:"var(--bg-primary)",borderRadius:"14px",padding:"24px !important",boxSizing:"border-box",display:"flex",flexDirection:"column",gap:"18px"}});const customDivider=document.querySelector(".ad-spend-single-marketplace-divider");customDivider&&customDivider.style.background&&operations.push({type:"style",element:customDivider,styles:{background:"var(--text-primary)"}});const headerDivider=document.querySelector(".ad-spend-header-divider");headerDivider&&headerDivider.style.background&&operations.push({type:"style",element:headerDivider,styles:{background:"var(--text-primary)"}}),operations.length>0&&await window.DOMOptimizer.batchStyleChanges(operations)}}async function updateListingProductImageBackgroundsOptimized(listingElements=null){const listings=listingElements||document.querySelectorAll(".listing-analytics-div");if(listings.length===0)return;const operations=[];listings.forEach(listingElement=>{const productImg=listingElement.querySelector(".listing-product-img"),orderedColorsBadge=listingElement.querySelector(".ordered-colors-badge");if(!productImg)return;let backgroundColor="#000000";if(orderedColorsBadge){const firstColorItem=orderedColorsBadge.querySelector(".color-item");if(firstColorItem){const firstColorCircle=firstColorItem.querySelector(".color-circle");if(firstColorCircle){const style=firstColorCircle.getAttribute("style");if(style){const colorMatch=style.match(/background-color:\s*([^;]+)/);colorMatch&&colorMatch[1]&&(backgroundColor=colorMatch[1].trim())}}}}const isWhiteColor=backgroundColor.includes("255, 255, 255")||backgroundColor==="#FFFFFF"||backgroundColor==="#ffffff"||backgroundColor==="rgb(255, 255, 255)"||backgroundColor==="white",isLightMode=document.documentElement.getAttribute("data-theme")!=="dark",styles={backgroundColor};isWhiteColor&&isLightMode?styles.border="1px solid rgba(96, 111, 149, 0.15)":styles.border="none",operations.push({type:"style",element:productImg,styles})}),operations.length>0&&await window.DOMOptimizer.batchStyleChanges(operations)}async function optimizedMarketplaceFiltering(selectedMarketplace){const allListings=document.querySelectorAll(".listing-analytics-div");allListings.length!==0&&await window.DOMOptimizer.optimizeMarketplaceFiltering(allListings,selectedMarketplace)}function animateProgressBarsOptimized(){const progressBars=document.querySelectorAll(".progress-fill");if(progressBars.length===0)return;const originalWidths=[];progressBars.forEach((bar,index)=>{originalWidths[index]=bar.style.width,bar.style.width="0"}),requestAnimationFrame(()=>{setTimeout(()=>{const operations=[];progressBars.forEach((bar,index)=>{operations.push({type:"style",element:bar,styles:{width:originalWidths[index]}})}),window.DOMOptimizer.batchStyleChanges(operations).then(()=>{})},100)})}async function updateButtonStatesOptimized(buttons,isDisabled){if(!buttons||buttons.length===0)return;const operations=[];buttons.forEach(button=>{isDisabled?operations.push({type:"style",element:button,styles:{pointerEvents:"none",opacity:"0.5",cursor:"not-allowed"}}):operations.push({type:"style",element:button,styles:{pointerEvents:"auto",opacity:"1",cursor:"pointer"}})}),operations.length>0&&await window.DOMOptimizer.batchStyleChanges(operations)}function createVirtualScrollForListings(container,items,itemHeight=120){return window.DOMOptimizer.createVirtualScrollList(container,items,itemHeight,(item,index)=>{const listingElement=document.createElement("div");return listingElement.className="listing-analytics-div virtual-listing",listingElement.innerHTML=item.html||`<div>Listing ${index}</div>`,listingElement})}async function updateSalesCardClassesOptimized(salesCards,classUpdates){if(!salesCards||salesCards.length===0)return;const operations=[];salesCards.forEach((salesCard,index)=>{const updates=classUpdates[index];updates&&operations.push({type:"class",element:salesCard,add:updates.add||[],remove:updates.remove||[],toggle:updates.toggle})}),operations.length>0&&await window.DOMOptimizer.batchStyleChanges(operations)}function initializeDOMOptimizations(){window.updateContainerStylesForTheme=updateContainerStylesForThemeOptimized,window.updateListingProductImageBackgroundsOptimized=updateListingProductImageBackgroundsOptimized,window.optimizedMarketplaceFiltering=optimizedMarketplaceFiltering,window.animateProgressBarsOptimized=animateProgressBarsOptimized,window.updateButtonStatesOptimized=updateButtonStatesOptimized,window.createVirtualScrollForListings=createVirtualScrollForListings,window.updateSalesCardClassesOptimized=updateSalesCardClassesOptimized}const GlobalShowHideDropdownManager={activeDropdown:null,dropdowns:new Map,register(id,showFn,hideFn){this.dropdowns.set(id,{show:showFn,hide:hideFn}),window.GlobalDropdownManager&&GlobalDropdownManager.register(id,showFn,hideFn,"show-hide")},show(id){if(window.GlobalDropdownManager)GlobalDropdownManager.show(id);else{if(this.activeDropdown&&this.activeDropdown!==id){const activeDropdownFns=this.dropdowns.get(this.activeDropdown);activeDropdownFns&&activeDropdownFns.hide()}const dropdownFns=this.dropdowns.get(id);dropdownFns&&(dropdownFns.show(),this.activeDropdown=id)}},hide(id){if(window.GlobalDropdownManager)GlobalDropdownManager.hide(id);else{const dropdownFns=this.dropdowns.get(id);dropdownFns&&(dropdownFns.hide(),this.activeDropdown===id&&(this.activeDropdown=null))}},hideAll(){this.dropdowns.forEach(fns=>{fns.hide()}),this.activeDropdown=null}};window.GlobalShowHideDropdownManager=GlobalShowHideDropdownManager,window.GlobalDropdownManager&&GlobalDropdownManager.registerSubManager("showHide",GlobalShowHideDropdownManager);function initializeShowHideOptionsButtons(){initializeShowHideOptionsForChart("last-week","Last Week's Sales"),initializeShowHideOptionsForChart("today-vs-previous","Today vs Previous Years"),initializeShowHideOptionsForChart("monthly","Monthly Sales"),initializeShowHideOptionsForChart("yearly","Yearly Sales"),document.addEventListener("click",e=>{!e.target.closest(".database-marketplace-dropdown")&&!e.target.closest(".show-hide-options-dropdown")&&!e.target.closest(".show-hide-options-btn")&&!e.target.closest(".compare-dropdown")&&!e.target.closest(".compare-btn")&&!e.target.closest(".snap-dropdown")&&!e.target.closest(".dropdown-header")&&!e.target.closest(".dropdown-menu")&&window.GlobalDropdownManager&&GlobalDropdownManager.hideAll()})}function initializeShowHideOptionsForChart(chartType,chartName){const showHideBtn=document.querySelector(`#${chartType}-show-hide-dropdown`).parentElement.querySelector(".show-hide-options-btn"),showHideDropdown=document.querySelector(`#${chartType}-show-hide-dropdown`),dropdownItems=document.querySelectorAll(`#${chartType}-show-hide-dropdown .show-hide-options-dropdown-item`);if(!showHideBtn||!showHideDropdown||dropdownItems.length===0)return;let dropdownVisible=!1;const showReturns=sessionStorage.getItem(`showReturns_${chartType}`)==="true",showRoyalties=sessionStorage.getItem(`showRoyalties_${chartType}`)==="true";updateShowHideCheckboxState(chartType,"returns",showReturns),updateShowHideCheckboxState(chartType,"royalties",showRoyalties),setTimeout(()=>{updateChartDataForShowHideOptions(chartType)},100);function showShowHideDropdown(){showHideDropdown.classList.add("show"),showHideBtn.classList.add("active"),dropdownVisible=!0}function hideShowHideDropdown(){showHideDropdown.classList.remove("show"),showHideBtn.classList.remove("active"),dropdownVisible=!1}chartType==="monthly"&&window.MonthlySalesDropdownManager?MonthlySalesDropdownManager.register(`showHideDropdown_${chartType}`,showShowHideDropdown,hideShowHideDropdown):GlobalShowHideDropdownManager.register(`showHideDropdown_${chartType}`,showShowHideDropdown,hideShowHideDropdown),showHideBtn.addEventListener("click",e=>{e.stopPropagation(),chartType==="monthly"&&window.MonthlySalesDropdownManager?dropdownVisible?MonthlySalesDropdownManager.hide(`showHideDropdown_${chartType}`):MonthlySalesDropdownManager.show(`showHideDropdown_${chartType}`):dropdownVisible?GlobalShowHideDropdownManager.hide(`showHideDropdown_${chartType}`):GlobalShowHideDropdownManager.show(`showHideDropdown_${chartType}`)}),dropdownItems.forEach(item=>{item.addEventListener("click",e=>{e.stopPropagation();const option=item.getAttribute("data-option");toggleShowHideOption(chartType,option)})})}function updateShowHideCheckboxState(chartType,option,isChecked){const checkboxImg=document.querySelector(`#${chartType}-show-hide-dropdown .show-hide-options-dropdown-item[data-option="${option}"] .show-hide-options-checkbox img`);checkboxImg&&(checkboxImg.src=`./assets/${isChecked?"checkbox-ic":"uncheckedbox-ic"}.svg`),sessionStorage.setItem(`show${option.charAt(0).toUpperCase()+option.slice(1)}_${chartType}`,isChecked.toString())}function toggleShowHideOption(chartType,option){const newState=!(sessionStorage.getItem(`show${option.charAt(0).toUpperCase()+option.slice(1)}_${chartType}`)==="true");updateShowHideCheckboxState(chartType,option,newState),updateChartDataForShowHideOptions(chartType)}function updateChartDataForShowHideOptions(chartType){const showReturns=sessionStorage.getItem(`showReturns_${chartType}`)==="true",showRoyalties=sessionStorage.getItem(`showRoyalties_${chartType}`)==="true";let chartContainer;switch(chartType){case"last-week":chartContainer=document.querySelector("#last-week-chart-container");break;case"today-vs-previous":chartContainer=document.querySelector("#today-vs-previous-years-chart-container");break;case"monthly":chartContainer=document.querySelector("#monthly-sales-chart-container");break;case"yearly":chartContainer=document.querySelector("#yearly-sales-chart-container");break}if(!chartContainer||!chartContainer.snapChart){const counters=window.__showHideRetryCounts=window.__showHideRetryCounts||{},nextAttempt=(counters[chartType]||0)+1;counters[chartType]=nextAttempt,nextAttempt<=20&&setTimeout(()=>updateChartDataForShowHideOptions(chartType),200);return}const chart=chartContainer.snapChart;window.__showHideRetryCounts&&(window.__showHideRetryCounts[chartType]=0),chart.options.showReturns=showReturns,chart.options.showRoyalties=showRoyalties,chart.render()}function getOriginalChartData(chartType){switch(chartType){case"last-week":return window.lastWeekOriginalData;case"today-vs-previous":return window.todayVsPreviousYearsOriginalData;case"monthly":return originalMonthlySalesData;case"yearly":return originalYearlySalesData;default:return null}}function filterChartDataForShowHide(data,showReturns,showRoyalties){return!data||!Array.isArray(data)?data:data.map(dataPoint=>{const filteredPoint={...dataPoint};return showReturns||(delete filteredPoint.returns,filteredPoint.marketplaces&&(filteredPoint.marketplaces=filteredPoint.marketplaces.map(mp=>{const filteredMp={...mp};return delete filteredMp.returns,filteredMp}))),showRoyalties||(delete filteredPoint.royalties,filteredPoint.marketplaces&&(filteredPoint.marketplaces=filteredPoint.marketplaces.map(mp=>{const filteredMp={...mp};return delete filteredMp.royalties,filteredMp}))),filteredPoint})}const NotificationManagerTest=function(){const snapIconPath="./assets/snap-logo.png",audioPath="./assets/audio/snap-new-sale.wav",titlePrefixesCommon=["Good news!","Cha\u2011ching!","Boom!","That\u2019s a hit!"];let lastPrefix=null,audioElement=null;function ensureAudioLoaded(){if(audioElement)return audioElement;const audio=new Audio(audioPath);return audio.preload="auto",audio.volume=1,audioElement=audio,audioElement}function pickPrefix(){if(titlePrefixesCommon.length===0)return"";let prefix=titlePrefixesCommon[Math.floor(Math.random()*titlePrefixesCommon.length)];if(lastPrefix&&titlePrefixesCommon.length>1){let attempts=0;for(;prefix===lastPrefix&&attempts<3;)prefix=titlePrefixesCommon[Math.floor(Math.random()*titlePrefixesCommon.length)],attempts+=1}return lastPrefix=prefix,prefix}function buildTitle(headlineQty){const prefix=pickPrefix(),productWord=headlineQty>1?"Products":"Product";return`${prefix} ${headlineQty} ${productWord} sold.`}function buildBody(flagEmoji,productQty,productTitle,marketplaceCode){const safeTitle=(productTitle||"Product").toString(),trimmed=safeTitle.length>80?`${safeTitle.slice(0,77)}\u2026`:safeTitle,ua=navigator.userAgent||"";return`Snap for MOD
${/Windows/i.test(ua)?marketplaceCode||"":flagEmoji||""} ${productQty}x ${trimmed}`}async function requestPermissionIfNeeded(){if(!("Notification"in window))return!1;if(Notification.permission==="granted")return!0;if(Notification.permission==="denied")return!1;try{return await Notification.requestPermission()==="granted"}catch{return!1}}async function playSound(){try{await ensureAudioLoaded().play()}catch{}}async function notifySale(params){const{topQuantity,productQuantity,productTitle,flagEmoji,marketplaceCode,quantity}=params;if(!await requestPermissionIfNeeded())return null;const headlineQty=topQuantity??quantity??productQuantity??1,bodyQty=productQuantity??quantity??headlineQty,title=buildTitle(headlineQty),body=buildBody(flagEmoji,bodyQty,productTitle,marketplaceCode),notification=new Notification(title,{body,icon:snapIconPath,silent:!1,timestamp:Date.now()});return notification.onclick=()=>{try{window.focus();const target=document.querySelector(".todays-sales-card-div");target&&typeof target.scrollIntoView=="function"&&target.scrollIntoView({behavior:"smooth",block:"start"})}catch{}},playSound(),notification}return{notifySale}}();window.NotificationManagerTest=NotificationManagerTest;const SalesMockSimulator=function(){const marketplaceWeights=[{code:"US",weight:.5,emoji:"\u{1F1FA}\u{1F1F8}"},{code:"UK",weight:.15,emoji:"\u{1F1EC}\u{1F1E7}"},{code:"DE",weight:.15,emoji:"\u{1F1E9}\u{1F1EA}"},{code:"FR",weight:.07,emoji:"\u{1F1EB}\u{1F1F7}"},{code:"IT",weight:.05,emoji:"\u{1F1EE}\u{1F1F9}"},{code:"ES",weight:.05,emoji:"\u{1F1EA}\u{1F1F8}"},{code:"JP",weight:.03,emoji:"\u{1F1EF}\u{1F1F5}"}];let active=!1,intervalId=null,intervalMs=3e4;function pickTopQuantity(){const roll=Math.random();return roll<.6?1:roll<.85?2:roll<.95?3:4+Math.floor(Math.random()*2)}function pickProductQuantity(maxTopQty){const roll=Math.random();let q=1;return roll<.7?q=1:roll<.9?q=2:q=3,Math.max(1,Math.min(q,Math.max(1,maxTopQty)))}function pickMarketplace(){const r=Math.random();let cumulative=0;for(const m of marketplaceWeights)if(cumulative+=m.weight,r<=cumulative)return m;return marketplaceWeights[0]}function getRandomProductTitle(){const visibleTitles=Array.from(document.querySelectorAll(".todays-sales-card-div .listing-title")).map(el=>el.textContent.trim()).filter(Boolean);if(visibleTitles.length>0)return visibleTitles[Math.floor(Math.random()*visibleTitles.length)];const fallback=["Cool Viking Yggdrasil Norse Graphic T\u2011Shirt","Retro Baseball Raglan Classic Tee","Minimalist Mountain Coffee Tumbler","Vintage Band Tour Graphic Tee","Kawaii Anime Character Phone Case"];return fallback[Math.floor(Math.random()*fallback.length)]}function updateTodaysSalesCountBy(quantity){const todayCard=document.querySelector(".todays-sales-card-div");if(!todayCard)return;const countEl=todayCard.querySelector(".sales-count"),newTotal=(countEl&&parseInt(countEl.textContent.replace(/[^0-9]/g,""),10)||0)+quantity;typeof handleRealTimeDataUpdate=="function"?handleRealTimeDataUpdate({analytics:[{totalSales:newTotal}]}):countEl&&(countEl.textContent=String(newTotal))}async function tick(){try{if(Math.random()>=.45)return;const topQty=pickTopQuantity(),productQty=pickProductQuantity(topQty),marketplace=pickMarketplace(),productTitle=getRandomProductTitle();updateTodaysSalesCountBy(topQty),await NotificationManagerTest.notifySale({topQuantity:topQty,productQuantity:productQty,productTitle,flagEmoji:marketplace.emoji,marketplaceCode:marketplace.code})}catch{}}function start(pollMs=3e4){return active?!1:(active=!0,intervalMs=pollMs,window.EventCleanupManager&&typeof window.EventCleanupManager.setInterval=="function"?intervalId=window.EventCleanupManager.setInterval(tick,intervalMs):intervalId=setInterval(tick,intervalMs),!0)}function stop(){return active?(active=!1,intervalId&&(window.EventCleanupManager&&typeof window.EventCleanupManager.clearInterval=="function"?window.EventCleanupManager.clearInterval(intervalId):clearInterval(intervalId),intervalId=null),!0):!1}function getStatus(){return{active,intervalMs}}return{start,stop,getStatus}}();if(window.SalesMockSimulator=SalesMockSimulator,window.__TEST_REALTIME_SALES__===!0)try{window.SalesMockSimulator.start()}catch{}
